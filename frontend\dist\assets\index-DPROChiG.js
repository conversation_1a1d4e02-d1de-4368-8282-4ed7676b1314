(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&n(l)}).observe(document,{childList:!0,subtree:!0});function s(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(o){if(o.ep)return;o.ep=!0;const i=s(o);fetch(o.href,i)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Zn(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const fe={},Yt=[],ht=()=>{},Ui=()=>!1,rn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Jn=e=>e.startsWith("onUpdate:"),Me=Object.assign,Qn=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Ni=Object.prototype.hasOwnProperty,re=(e,t)=>Ni.call(e,t),z=Array.isArray,Xt=e=>Ms(e)==="[object Map]",cs=e=>Ms(e)==="[object Set]",_o=e=>Ms(e)==="[object Date]",J=e=>typeof e=="function",ke=e=>typeof e=="string",vt=e=>typeof e=="symbol",me=e=>e!==null&&typeof e=="object",br=e=>(me(e)||J(e))&&J(e.then)&&J(e.catch),yr=Object.prototype.toString,Ms=e=>yr.call(e),ji=e=>Ms(e).slice(8,-1),_r=e=>Ms(e)==="[object Object]",Yn=e=>ke(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gs=Zn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ln=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Bi=/-(\w)/g,Ze=ln(e=>e.replace(Bi,(t,s)=>s?s.toUpperCase():"")),Hi=/\B([A-Z])/g,Vt=ln(e=>e.replace(Hi,"-$1").toLowerCase()),an=ln(e=>e.charAt(0).toUpperCase()+e.slice(1)),yn=ln(e=>e?`on${an(e)}`:""),Mt=(e,t)=>!Object.is(e,t),Fs=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},On=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Qs=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ko;const cn=()=>ko||(ko=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function un(e){if(z(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],o=ke(n)?zi(n):un(n);if(o)for(const i in o)t[i]=o[i]}return t}else if(ke(e)||me(e))return e}const Fi=/;(?![^(]*\))/g,Di=/:([^]+)/,Ki=/\/\*[^]*?\*\//g;function zi(e){const t={};return e.replace(Ki,"").split(Fi).forEach(s=>{if(s){const n=s.split(Di);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function ce(e){let t="";if(ke(e))t=e;else if(z(e))for(let s=0;s<e.length;s++){const n=ce(e[s]);n&&(t+=n+" ")}else if(me(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Wi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",qi=Zn(Wi);function kr(e){return!!e||e===""}function Gi(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=Dt(e[n],t[n]);return s}function Dt(e,t){if(e===t)return!0;let s=_o(e),n=_o(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=vt(e),n=vt(t),s||n)return e===t;if(s=z(e),n=z(t),s||n)return s&&n?Gi(e,t):!1;if(s=me(e),n=me(t),s||n){if(!s||!n)return!1;const o=Object.keys(e).length,i=Object.keys(t).length;if(o!==i)return!1;for(const l in e){const c=e.hasOwnProperty(l),a=t.hasOwnProperty(l);if(c&&!a||!c&&a||!Dt(e[l],t[l]))return!1}}return String(e)===String(t)}function Xn(e,t){return e.findIndex(s=>Dt(s,t))}const Cr=e=>!!(e&&e.__v_isRef===!0),M=e=>ke(e)?e:e==null?"":z(e)||me(e)&&(e.toString===yr||!J(e.toString))?Cr(e)?M(e.value):JSON.stringify(e,xr,2):String(e),xr=(e,t)=>Cr(t)?xr(e,t.value):Xt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,o],i)=>(s[_n(n,i)+" =>"]=o,s),{})}:cs(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>_n(s))}:vt(t)?_n(t):me(t)&&!z(t)&&!_r(t)?String(t):t,_n=(e,t="")=>{var s;return vt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let je;class $r{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=je,!t&&je&&(this.index=(je.scopes||(je.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=je;try{return je=this,t()}finally{je=s}}}on(){++this._on===1&&(this.prevScope=je,je=this)}off(){this._on>0&&--this._on===0&&(je=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Zi(e){return new $r(e)}function Ji(){return je}let ge;const kn=new WeakSet;class Sr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,je&&je.active&&je.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,kn.has(this)&&(kn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Lr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Co(this),Pr(this);const t=ge,s=tt;ge=this,tt=!0;try{return this.fn()}finally{Rr(this),ge=t,tt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)so(t);this.deps=this.depsTail=void 0,Co(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?kn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){In(this)&&this.run()}get dirty(){return In(this)}}let Er=0,ms,ws;function Lr(e,t=!1){if(e.flags|=8,t){e.next=ws,ws=e;return}e.next=ms,ms=e}function eo(){Er++}function to(){if(--Er>0)return;if(ws){let t=ws;for(ws=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;ms;){let t=ms;for(ms=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Pr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Rr(e){let t,s=e.depsTail,n=s;for(;n;){const o=n.prevDep;n.version===-1?(n===s&&(s=o),so(n),Qi(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o}e.deps=t,e.depsTail=s}function In(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ar(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ar(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===$s)||(e.globalVersion=$s,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!In(e))))return;e.flags|=2;const t=e.dep,s=ge,n=tt;ge=e,tt=!0;try{Pr(e);const o=e.fn(e._value);(t.version===0||Mt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{ge=s,tt=n,Rr(e),e.flags&=-3}}function so(e,t=!1){const{dep:s,prevSub:n,nextSub:o}=e;if(n&&(n.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)so(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Qi(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let tt=!0;const Mr=[];function Ct(){Mr.push(tt),tt=!1}function xt(){const e=Mr.pop();tt=e===void 0?!0:e}function Co(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ge;ge=void 0;try{t()}finally{ge=s}}}let $s=0;class Yi{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class no{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ge||!tt||ge===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ge)s=this.activeLink=new Yi(ge,this),ge.deps?(s.prevDep=ge.depsTail,ge.depsTail.nextDep=s,ge.depsTail=s):ge.deps=ge.depsTail=s,Tr(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=ge.depsTail,s.nextDep=void 0,ge.depsTail.nextDep=s,ge.depsTail=s,ge.deps===s&&(ge.deps=n)}return s}trigger(t){this.version++,$s++,this.notify(t)}notify(t){eo();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{to()}}}function Tr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Tr(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Vn=new WeakMap,Ft=Symbol(""),Un=Symbol(""),Ss=Symbol("");function Pe(e,t,s){if(tt&&ge){let n=Vn.get(e);n||Vn.set(e,n=new Map);let o=n.get(s);o||(n.set(s,o=new no),o.map=n,o.key=s),o.track()}}function yt(e,t,s,n,o,i){const l=Vn.get(e);if(!l){$s++;return}const c=a=>{a&&a.trigger()};if(eo(),t==="clear")l.forEach(c);else{const a=z(e),d=a&&Yn(s);if(a&&s==="length"){const u=Number(n);l.forEach((p,g)=>{(g==="length"||g===Ss||!vt(g)&&g>=u)&&c(p)})}else switch((s!==void 0||l.has(void 0))&&c(l.get(s)),d&&c(l.get(Ss)),t){case"add":a?d&&c(l.get("length")):(c(l.get(Ft)),Xt(e)&&c(l.get(Un)));break;case"delete":a||(c(l.get(Ft)),Xt(e)&&c(l.get(Un)));break;case"set":Xt(e)&&c(l.get(Ft));break}}to()}function Zt(e){const t=oe(e);return t===e?t:(Pe(t,"iterate",Ss),qe(e)?t:t.map(Se))}function dn(e){return Pe(e=oe(e),"iterate",Ss),e}const Xi={__proto__:null,[Symbol.iterator](){return Cn(this,Symbol.iterator,Se)},concat(...e){return Zt(this).concat(...e.map(t=>z(t)?Zt(t):t))},entries(){return Cn(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return mt(this,"every",e,t,void 0,arguments)},filter(e,t){return mt(this,"filter",e,t,s=>s.map(Se),arguments)},find(e,t){return mt(this,"find",e,t,Se,arguments)},findIndex(e,t){return mt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return mt(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return mt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return mt(this,"forEach",e,t,void 0,arguments)},includes(...e){return xn(this,"includes",e)},indexOf(...e){return xn(this,"indexOf",e)},join(e){return Zt(this).join(e)},lastIndexOf(...e){return xn(this,"lastIndexOf",e)},map(e,t){return mt(this,"map",e,t,void 0,arguments)},pop(){return fs(this,"pop")},push(...e){return fs(this,"push",e)},reduce(e,...t){return xo(this,"reduce",e,t)},reduceRight(e,...t){return xo(this,"reduceRight",e,t)},shift(){return fs(this,"shift")},some(e,t){return mt(this,"some",e,t,void 0,arguments)},splice(...e){return fs(this,"splice",e)},toReversed(){return Zt(this).toReversed()},toSorted(e){return Zt(this).toSorted(e)},toSpliced(...e){return Zt(this).toSpliced(...e)},unshift(...e){return fs(this,"unshift",e)},values(){return Cn(this,"values",Se)}};function Cn(e,t,s){const n=dn(e),o=n[t]();return n!==e&&!qe(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=s(i.value)),i}),o}const el=Array.prototype;function mt(e,t,s,n,o,i){const l=dn(e),c=l!==e&&!qe(e),a=l[t];if(a!==el[t]){const p=a.apply(e,i);return c?Se(p):p}let d=s;l!==e&&(c?d=function(p,g){return s.call(this,Se(p),g,e)}:s.length>2&&(d=function(p,g){return s.call(this,p,g,e)}));const u=a.call(l,d,n);return c&&o?o(u):u}function xo(e,t,s,n){const o=dn(e);let i=s;return o!==e&&(qe(e)?s.length>3&&(i=function(l,c,a){return s.call(this,l,c,a,e)}):i=function(l,c,a){return s.call(this,l,Se(c),a,e)}),o[t](i,...n)}function xn(e,t,s){const n=oe(e);Pe(n,"iterate",Ss);const o=n[t](...s);return(o===-1||o===!1)&&io(s[0])?(s[0]=oe(s[0]),n[t](...s)):o}function fs(e,t,s=[]){Ct(),eo();const n=oe(e)[t].apply(e,s);return to(),xt(),n}const tl=Zn("__proto__,__v_isRef,__isVue"),Or=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(vt));function sl(e){vt(e)||(e=String(e));const t=oe(this);return Pe(t,"has",e),t.hasOwnProperty(e)}class Ir{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const o=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!o;if(s==="__v_isReadonly")return o;if(s==="__v_isShallow")return i;if(s==="__v_raw")return n===(o?i?fl:jr:i?Nr:Ur).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const l=z(t);if(!o){let a;if(l&&(a=Xi[s]))return a;if(s==="hasOwnProperty")return sl}const c=Reflect.get(t,s,Ae(t)?t:n);return(vt(s)?Or.has(s):tl(s))||(o||Pe(t,"get",s),i)?c:Ae(c)?l&&Yn(s)?c:c.value:me(c)?o?Hr(c):xe(c):c}}class Vr extends Ir{constructor(t=!1){super(!1,t)}set(t,s,n,o){let i=t[s];if(!this._isShallow){const a=Tt(i);if(!qe(n)&&!Tt(n)&&(i=oe(i),n=oe(n)),!z(t)&&Ae(i)&&!Ae(n))return a?!1:(i.value=n,!0)}const l=z(t)&&Yn(s)?Number(s)<t.length:re(t,s),c=Reflect.set(t,s,n,Ae(t)?t:o);return t===oe(o)&&(l?Mt(n,i)&&yt(t,"set",s,n):yt(t,"add",s,n)),c}deleteProperty(t,s){const n=re(t,s);t[s];const o=Reflect.deleteProperty(t,s);return o&&n&&yt(t,"delete",s,void 0),o}has(t,s){const n=Reflect.has(t,s);return(!vt(s)||!Or.has(s))&&Pe(t,"has",s),n}ownKeys(t){return Pe(t,"iterate",z(t)?"length":Ft),Reflect.ownKeys(t)}}class nl extends Ir{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const ol=new Vr,rl=new nl,il=new Vr(!0);const Nn=e=>e,Us=e=>Reflect.getPrototypeOf(e);function ll(e,t,s){return function(...n){const o=this.__v_raw,i=oe(o),l=Xt(i),c=e==="entries"||e===Symbol.iterator&&l,a=e==="keys"&&l,d=o[e](...n),u=s?Nn:t?Ys:Se;return!t&&Pe(i,"iterate",a?Un:Ft),{next(){const{value:p,done:g}=d.next();return g?{value:p,done:g}:{value:c?[u(p[0]),u(p[1])]:u(p),done:g}},[Symbol.iterator](){return this}}}}function Ns(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function al(e,t){const s={get(o){const i=this.__v_raw,l=oe(i),c=oe(o);e||(Mt(o,c)&&Pe(l,"get",o),Pe(l,"get",c));const{has:a}=Us(l),d=t?Nn:e?Ys:Se;if(a.call(l,o))return d(i.get(o));if(a.call(l,c))return d(i.get(c));i!==l&&i.get(o)},get size(){const o=this.__v_raw;return!e&&Pe(oe(o),"iterate",Ft),Reflect.get(o,"size",o)},has(o){const i=this.__v_raw,l=oe(i),c=oe(o);return e||(Mt(o,c)&&Pe(l,"has",o),Pe(l,"has",c)),o===c?i.has(o):i.has(o)||i.has(c)},forEach(o,i){const l=this,c=l.__v_raw,a=oe(c),d=t?Nn:e?Ys:Se;return!e&&Pe(a,"iterate",Ft),c.forEach((u,p)=>o.call(i,d(u),d(p),l))}};return Me(s,e?{add:Ns("add"),set:Ns("set"),delete:Ns("delete"),clear:Ns("clear")}:{add(o){!t&&!qe(o)&&!Tt(o)&&(o=oe(o));const i=oe(this);return Us(i).has.call(i,o)||(i.add(o),yt(i,"add",o,o)),this},set(o,i){!t&&!qe(i)&&!Tt(i)&&(i=oe(i));const l=oe(this),{has:c,get:a}=Us(l);let d=c.call(l,o);d||(o=oe(o),d=c.call(l,o));const u=a.call(l,o);return l.set(o,i),d?Mt(i,u)&&yt(l,"set",o,i):yt(l,"add",o,i),this},delete(o){const i=oe(this),{has:l,get:c}=Us(i);let a=l.call(i,o);a||(o=oe(o),a=l.call(i,o)),c&&c.call(i,o);const d=i.delete(o);return a&&yt(i,"delete",o,void 0),d},clear(){const o=oe(this),i=o.size!==0,l=o.clear();return i&&yt(o,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(o=>{s[o]=ll(o,e,t)}),s}function oo(e,t){const s=al(e,t);return(n,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?n:Reflect.get(re(s,o)&&o in n?s:n,o,i)}const cl={get:oo(!1,!1)},ul={get:oo(!1,!0)},dl={get:oo(!0,!1)};const Ur=new WeakMap,Nr=new WeakMap,jr=new WeakMap,fl=new WeakMap;function pl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function hl(e){return e.__v_skip||!Object.isExtensible(e)?0:pl(ji(e))}function xe(e){return Tt(e)?e:ro(e,!1,ol,cl,Ur)}function Br(e){return ro(e,!1,il,ul,Nr)}function Hr(e){return ro(e,!0,rl,dl,jr)}function ro(e,t,s,n,o){if(!me(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=hl(e);if(i===0)return e;const l=o.get(e);if(l)return l;const c=new Proxy(e,i===2?n:s);return o.set(e,c),c}function es(e){return Tt(e)?es(e.__v_raw):!!(e&&e.__v_isReactive)}function Tt(e){return!!(e&&e.__v_isReadonly)}function qe(e){return!!(e&&e.__v_isShallow)}function io(e){return e?!!e.__v_raw:!1}function oe(e){const t=e&&e.__v_raw;return t?oe(t):e}function Fr(e){return!re(e,"__v_skip")&&Object.isExtensible(e)&&On(e,"__v_skip",!0),e}const Se=e=>me(e)?xe(e):e,Ys=e=>me(e)?Hr(e):e;function Ae(e){return e?e.__v_isRef===!0:!1}function W(e){return Dr(e,!1)}function vl(e){return Dr(e,!0)}function Dr(e,t){return Ae(e)?e:new gl(e,t)}class gl{constructor(t,s){this.dep=new no,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:oe(t),this._value=s?t:Se(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||qe(t)||Tt(t);t=n?t:oe(t),Mt(t,s)&&(this._rawValue=t,this._value=n?t:Se(t),this.dep.trigger())}}function ye(e){return Ae(e)?e.value:e}const ml={get:(e,t,s)=>t==="__v_raw"?e:ye(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const o=e[t];return Ae(o)&&!Ae(s)?(o.value=s,!0):Reflect.set(e,t,s,n)}};function Kr(e){return es(e)?e:new Proxy(e,ml)}class wl{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new no(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=$s-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ge!==this)return Lr(this,!0),!0}get value(){const t=this.dep.track();return Ar(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function bl(e,t,s=!1){let n,o;return J(e)?n=e:(n=e.get,o=e.set),new wl(n,o,s)}const js={},Xs=new WeakMap;let Bt;function yl(e,t=!1,s=Bt){if(s){let n=Xs.get(s);n||Xs.set(s,n=[]),n.push(e)}}function _l(e,t,s=fe){const{immediate:n,deep:o,once:i,scheduler:l,augmentJob:c,call:a}=s,d=j=>o?j:qe(j)||o===!1||o===0?_t(j,1):_t(j);let u,p,g,v,y=!1,w=!1;if(Ae(e)?(p=()=>e.value,y=qe(e)):es(e)?(p=()=>d(e),y=!0):z(e)?(w=!0,y=e.some(j=>es(j)||qe(j)),p=()=>e.map(j=>{if(Ae(j))return j.value;if(es(j))return d(j);if(J(j))return a?a(j,2):j()})):J(e)?t?p=a?()=>a(e,2):e:p=()=>{if(g){Ct();try{g()}finally{xt()}}const j=Bt;Bt=u;try{return a?a(e,3,[v]):e(v)}finally{Bt=j}}:p=ht,t&&o){const j=p,q=o===!0?1/0:o;p=()=>_t(j(),q)}const O=Ji(),U=()=>{u.stop(),O&&O.active&&Qn(O.effects,u)};if(i&&t){const j=t;t=(...q)=>{j(...q),U()}}let x=w?new Array(e.length).fill(js):js;const _=j=>{if(!(!(u.flags&1)||!u.dirty&&!j))if(t){const q=u.run();if(o||y||(w?q.some((ue,ie)=>Mt(ue,x[ie])):Mt(q,x))){g&&g();const ue=Bt;Bt=u;try{const ie=[q,x===js?void 0:w&&x[0]===js?[]:x,v];x=q,a?a(t,3,ie):t(...ie)}finally{Bt=ue}}}else u.run()};return c&&c(_),u=new Sr(p),u.scheduler=l?()=>l(_,!1):_,v=j=>yl(j,!1,u),g=u.onStop=()=>{const j=Xs.get(u);if(j){if(a)a(j,4);else for(const q of j)q();Xs.delete(u)}},t?n?_(!0):x=u.run():l?l(_.bind(null,!0),!0):u.run(),U.pause=u.pause.bind(u),U.resume=u.resume.bind(u),U.stop=U,U}function _t(e,t=1/0,s){if(t<=0||!me(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Ae(e))_t(e.value,t,s);else if(z(e))for(let n=0;n<e.length;n++)_t(e[n],t,s);else if(cs(e)||Xt(e))e.forEach(n=>{_t(n,t,s)});else if(_r(e)){for(const n in e)_t(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&_t(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ts(e,t,s,n){try{return n?e(...n):e()}catch(o){fn(o,t,s)}}function gt(e,t,s,n){if(J(e)){const o=Ts(e,t,s,n);return o&&br(o)&&o.catch(i=>{fn(i,t,s)}),o}if(z(e)){const o=[];for(let i=0;i<e.length;i++)o.push(gt(e[i],t,s,n));return o}}function fn(e,t,s,n=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||fe;if(t){let c=t.parent;const a=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const u=c.ec;if(u){for(let p=0;p<u.length;p++)if(u[p](e,a,d)===!1)return}c=c.parent}if(i){Ct(),Ts(i,null,10,[e,a,d]),xt();return}}kl(e,s,o,n,l)}function kl(e,t,s,n=!0,o=!1){if(o)throw e;console.error(e)}const Ie=[];let ft=-1;const ts=[];let Pt=null,Jt=0;const zr=Promise.resolve();let en=null;function lo(e){const t=en||zr;return e?t.then(this?e.bind(this):e):t}function Cl(e){let t=ft+1,s=Ie.length;for(;t<s;){const n=t+s>>>1,o=Ie[n],i=Es(o);i<e||i===e&&o.flags&2?t=n+1:s=n}return t}function ao(e){if(!(e.flags&1)){const t=Es(e),s=Ie[Ie.length-1];!s||!(e.flags&2)&&t>=Es(s)?Ie.push(e):Ie.splice(Cl(t),0,e),e.flags|=1,Wr()}}function Wr(){en||(en=zr.then(Gr))}function xl(e){z(e)?ts.push(...e):Pt&&e.id===-1?Pt.splice(Jt+1,0,e):e.flags&1||(ts.push(e),e.flags|=1),Wr()}function $o(e,t,s=ft+1){for(;s<Ie.length;s++){const n=Ie[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;Ie.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function qr(e){if(ts.length){const t=[...new Set(ts)].sort((s,n)=>Es(s)-Es(n));if(ts.length=0,Pt){Pt.push(...t);return}for(Pt=t,Jt=0;Jt<Pt.length;Jt++){const s=Pt[Jt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Pt=null,Jt=0}}const Es=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Gr(e){try{for(ft=0;ft<Ie.length;ft++){const t=Ie[ft];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ts(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ft<Ie.length;ft++){const t=Ie[ft];t&&(t.flags&=-2)}ft=-1,Ie.length=0,qr(),en=null,(Ie.length||ts.length)&&Gr()}}let De=null,Zr=null;function tn(e){const t=De;return De=e,Zr=e&&e.type.__scopeId||null,t}function pe(e,t=De,s){if(!t||e._n)return e;const n=(...o)=>{n._d&&Io(-1);const i=tn(t);let l;try{l=e(...o)}finally{tn(i),n._d&&Io(1)}return l};return n._n=!0,n._c=!0,n._d=!0,n}function de(e,t){if(De===null)return e;const s=gn(De),n=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[i,l,c,a=fe]=t[o];i&&(J(i)&&(i={mounted:i,updated:i}),i.deep&&_t(l),n.push({dir:i,instance:s,value:l,oldValue:void 0,arg:c,modifiers:a}))}return e}function Nt(e,t,s,n){const o=e.dirs,i=t&&t.dirs;for(let l=0;l<o.length;l++){const c=o[l];i&&(c.oldValue=i[l].value);let a=c.dir[n];a&&(Ct(),gt(a,s,8,[e.el,c,e,t]),xt())}}const $l=Symbol("_vte"),Sl=e=>e.__isTeleport;function co(e,t){e.shapeFlag&6&&e.component?(e.transition=t,co(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Be(e,t){return J(e)?Me({name:e.name},t,{setup:e}):e}function Jr(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function bs(e,t,s,n,o=!1){if(z(e)){e.forEach((y,w)=>bs(y,t&&(z(t)?t[w]:t),s,n,o));return}if(ys(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&bs(e,t,s,n.component.subTree);return}const i=n.shapeFlag&4?gn(n.component):n.el,l=o?null:i,{i:c,r:a}=e,d=t&&t.r,u=c.refs===fe?c.refs={}:c.refs,p=c.setupState,g=oe(p),v=p===fe?()=>!1:y=>re(g,y);if(d!=null&&d!==a&&(ke(d)?(u[d]=null,v(d)&&(p[d]=null)):Ae(d)&&(d.value=null)),J(a))Ts(a,c,12,[l,u]);else{const y=ke(a),w=Ae(a);if(y||w){const O=()=>{if(e.f){const U=y?v(a)?p[a]:u[a]:a.value;o?z(U)&&Qn(U,i):z(U)?U.includes(i)||U.push(i):y?(u[a]=[i],v(a)&&(p[a]=u[a])):(a.value=[i],e.k&&(u[e.k]=a.value))}else y?(u[a]=l,v(a)&&(p[a]=l)):w&&(a.value=l,e.k&&(u[e.k]=l))};l?(O.id=-1,Fe(O,s)):O()}}}cn().requestIdleCallback;cn().cancelIdleCallback;const ys=e=>!!e.type.__asyncLoader,Qr=e=>e.type.__isKeepAlive;function El(e,t){Yr(e,"a",t)}function Ll(e,t){Yr(e,"da",t)}function Yr(e,t,s=Re){const n=e.__wdc||(e.__wdc=()=>{let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(pn(t,n,s),s){let o=s.parent;for(;o&&o.parent;)Qr(o.parent.vnode)&&Pl(n,t,s,o),o=o.parent}}function Pl(e,t,s,n){const o=pn(t,e,n,!0);Os(()=>{Qn(n[t],o)},s)}function pn(e,t,s=Re,n=!1){if(s){const o=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...l)=>{Ct();const c=Is(s),a=gt(t,s,e,l);return c(),xt(),a});return n?o.unshift(i):o.push(i),i}}const $t=e=>(t,s=Re)=>{(!Ps||e==="sp")&&pn(e,(...n)=>t(...n),s)},Rl=$t("bm"),zt=$t("m"),Al=$t("bu"),Ml=$t("u"),Tl=$t("bum"),Os=$t("um"),Ol=$t("sp"),Il=$t("rtg"),Vl=$t("rtc");function Ul(e,t=Re){pn("ec",e,t)}const Nl="components";function ot(e,t){return Bl(Nl,e,!0,t)||e}const jl=Symbol.for("v-ndc");function Bl(e,t,s=!0,n=!1){const o=De||Re;if(o){const i=o.type;{const c=Ea(i,!1);if(c&&(c===t||c===Ze(t)||c===an(Ze(t))))return i}const l=So(o[e]||i[e],t)||So(o.appContext[e],t);return!l&&n?i:l}}function So(e,t){return e&&(e[t]||e[Ze(t)]||e[an(Ze(t))])}function et(e,t,s,n){let o;const i=s,l=z(e);if(l||ke(e)){const c=l&&es(e);let a=!1,d=!1;c&&(a=!qe(e),d=Tt(e),e=dn(e)),o=new Array(e.length);for(let u=0,p=e.length;u<p;u++)o[u]=t(a?d?Ys(Se(e[u])):Se(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){o=new Array(e);for(let c=0;c<e;c++)o[c]=t(c+1,c,void 0,i)}else if(me(e))if(e[Symbol.iterator])o=Array.from(e,(c,a)=>t(c,a,void 0,i));else{const c=Object.keys(e);o=new Array(c.length);for(let a=0,d=c.length;a<d;a++){const u=c[a];o[a]=t(e[u],u,a,i)}}else o=[];return o}const jn=e=>e?wi(e)?gn(e):jn(e.parent):null,_s=Me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>jn(e.parent),$root:e=>jn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ei(e),$forceUpdate:e=>e.f||(e.f=()=>{ao(e.update)}),$nextTick:e=>e.n||(e.n=lo.bind(e.proxy)),$watch:e=>la.bind(e)}),$n=(e,t)=>e!==fe&&!e.__isScriptSetup&&re(e,t),Hl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:o,props:i,accessCache:l,type:c,appContext:a}=e;let d;if(t[0]!=="$"){const v=l[t];if(v!==void 0)switch(v){case 1:return n[t];case 2:return o[t];case 4:return s[t];case 3:return i[t]}else{if($n(n,t))return l[t]=1,n[t];if(o!==fe&&re(o,t))return l[t]=2,o[t];if((d=e.propsOptions[0])&&re(d,t))return l[t]=3,i[t];if(s!==fe&&re(s,t))return l[t]=4,s[t];Bn&&(l[t]=0)}}const u=_s[t];let p,g;if(u)return t==="$attrs"&&Pe(e.attrs,"get",""),u(e);if((p=c.__cssModules)&&(p=p[t]))return p;if(s!==fe&&re(s,t))return l[t]=4,s[t];if(g=a.config.globalProperties,re(g,t))return g[t]},set({_:e},t,s){const{data:n,setupState:o,ctx:i}=e;return $n(o,t)?(o[t]=s,!0):n!==fe&&re(n,t)?(n[t]=s,!0):re(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:o,propsOptions:i}},l){let c;return!!s[l]||e!==fe&&re(e,l)||$n(t,l)||(c=i[0])&&re(c,l)||re(n,l)||re(_s,l)||re(o.config.globalProperties,l)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:re(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Eo(e){return z(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Bn=!0;function Fl(e){const t=ei(e),s=e.proxy,n=e.ctx;Bn=!1,t.beforeCreate&&Lo(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:l,watch:c,provide:a,inject:d,created:u,beforeMount:p,mounted:g,beforeUpdate:v,updated:y,activated:w,deactivated:O,beforeDestroy:U,beforeUnmount:x,destroyed:_,unmounted:j,render:q,renderTracked:ue,renderTriggered:ie,errorCaptured:F,serverPrefetch:C,expose:be,inheritAttrs:Q,components:Je,directives:We,filters:Ut}=t;if(d&&Dl(d,n,null),l)for(const te in l){const se=l[te];J(se)&&(n[te]=se.bind(s))}if(o){const te=o.call(s,s);me(te)&&(e.data=xe(te))}if(Bn=!0,i)for(const te in i){const se=i[te],Qe=J(se)?se.bind(s,s):J(se.get)?se.get.bind(s,s):ht,lt=!J(se)&&J(se.set)?se.set.bind(s):ht,$e=we({get:Qe,set:lt});Object.defineProperty(n,te,{enumerable:!0,configurable:!0,get:()=>$e.value,set:Ee=>$e.value=Ee})}if(c)for(const te in c)Xr(c[te],n,s,te);if(a){const te=J(a)?a.call(s):a;Reflect.ownKeys(te).forEach(se=>{Ds(se,te[se])})}u&&Lo(u,e,"c");function _e(te,se){z(se)?se.forEach(Qe=>te(Qe.bind(s))):se&&te(se.bind(s))}if(_e(Rl,p),_e(zt,g),_e(Al,v),_e(Ml,y),_e(El,w),_e(Ll,O),_e(Ul,F),_e(Vl,ue),_e(Il,ie),_e(Tl,x),_e(Os,j),_e(Ol,C),z(be))if(be.length){const te=e.exposed||(e.exposed={});be.forEach(se=>{Object.defineProperty(te,se,{get:()=>s[se],set:Qe=>s[se]=Qe,enumerable:!0})})}else e.exposed||(e.exposed={});q&&e.render===ht&&(e.render=q),Q!=null&&(e.inheritAttrs=Q),Je&&(e.components=Je),We&&(e.directives=We),C&&Jr(e)}function Dl(e,t,s=ht){z(e)&&(e=Hn(e));for(const n in e){const o=e[n];let i;me(o)?"default"in o?i=st(o.from||n,o.default,!0):i=st(o.from||n):i=st(o),Ae(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[n]=i}}function Lo(e,t,s){gt(z(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function Xr(e,t,s,n){let o=n.includes(".")?pi(s,n):()=>s[n];if(ke(e)){const i=t[e];J(i)&&Ks(o,i)}else if(J(e))Ks(o,e.bind(s));else if(me(e))if(z(e))e.forEach(i=>Xr(i,t,s,n));else{const i=J(e.handler)?e.handler.bind(s):t[e.handler];J(i)&&Ks(o,i,e)}}function ei(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,c=i.get(t);let a;return c?a=c:!o.length&&!s&&!n?a=t:(a={},o.length&&o.forEach(d=>sn(a,d,l,!0)),sn(a,t,l)),me(t)&&i.set(t,a),a}function sn(e,t,s,n=!1){const{mixins:o,extends:i}=t;i&&sn(e,i,s,!0),o&&o.forEach(l=>sn(e,l,s,!0));for(const l in t)if(!(n&&l==="expose")){const c=Kl[l]||s&&s[l];e[l]=c?c(e[l],t[l]):t[l]}return e}const Kl={data:Po,props:Ro,emits:Ro,methods:vs,computed:vs,beforeCreate:Oe,created:Oe,beforeMount:Oe,mounted:Oe,beforeUpdate:Oe,updated:Oe,beforeDestroy:Oe,beforeUnmount:Oe,destroyed:Oe,unmounted:Oe,activated:Oe,deactivated:Oe,errorCaptured:Oe,serverPrefetch:Oe,components:vs,directives:vs,watch:Wl,provide:Po,inject:zl};function Po(e,t){return t?e?function(){return Me(J(e)?e.call(this,this):e,J(t)?t.call(this,this):t)}:t:e}function zl(e,t){return vs(Hn(e),Hn(t))}function Hn(e){if(z(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Oe(e,t){return e?[...new Set([].concat(e,t))]:t}function vs(e,t){return e?Me(Object.create(null),e,t):t}function Ro(e,t){return e?z(e)&&z(t)?[...new Set([...e,...t])]:Me(Object.create(null),Eo(e),Eo(t??{})):t}function Wl(e,t){if(!e)return t;if(!t)return e;const s=Me(Object.create(null),e);for(const n in t)s[n]=Oe(e[n],t[n]);return s}function ti(){return{app:null,config:{isNativeTag:Ui,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ql=0;function Gl(e,t){return function(n,o=null){J(n)||(n=Me({},n)),o!=null&&!me(o)&&(o=null);const i=ti(),l=new WeakSet,c=[];let a=!1;const d=i.app={_uid:ql++,_component:n,_props:o,_container:null,_context:i,_instance:null,version:Pa,get config(){return i.config},set config(u){},use(u,...p){return l.has(u)||(u&&J(u.install)?(l.add(u),u.install(d,...p)):J(u)&&(l.add(u),u(d,...p))),d},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),d},component(u,p){return p?(i.components[u]=p,d):i.components[u]},directive(u,p){return p?(i.directives[u]=p,d):i.directives[u]},mount(u,p,g){if(!a){const v=d._ceVNode||X(n,o);return v.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(v,u,g),a=!0,d._container=u,u.__vue_app__=d,gn(v.component)}},onUnmount(u){c.push(u)},unmount(){a&&(gt(c,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,p){return i.provides[u]=p,d},runWithContext(u){const p=ss;ss=d;try{return u()}finally{ss=p}}};return d}}let ss=null;function Ds(e,t){if(Re){let s=Re.provides;const n=Re.parent&&Re.parent.provides;n===s&&(s=Re.provides=Object.create(n)),s[e]=t}}function st(e,t,s=!1){const n=ka();if(n||ss){let o=ss?ss._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return s&&J(t)?t.call(n&&n.proxy):t}}const si={},ni=()=>Object.create(si),oi=e=>Object.getPrototypeOf(e)===si;function Zl(e,t,s,n=!1){const o={},i=ni();e.propsDefaults=Object.create(null),ri(e,t,o,i);for(const l in e.propsOptions[0])l in o||(o[l]=void 0);s?e.props=n?o:Br(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Jl(e,t,s,n){const{props:o,attrs:i,vnode:{patchFlag:l}}=e,c=oe(o),[a]=e.propsOptions;let d=!1;if((n||l>0)&&!(l&16)){if(l&8){const u=e.vnode.dynamicProps;for(let p=0;p<u.length;p++){let g=u[p];if(hn(e.emitsOptions,g))continue;const v=t[g];if(a)if(re(i,g))v!==i[g]&&(i[g]=v,d=!0);else{const y=Ze(g);o[y]=Fn(a,c,y,v,e,!1)}else v!==i[g]&&(i[g]=v,d=!0)}}}else{ri(e,t,o,i)&&(d=!0);let u;for(const p in c)(!t||!re(t,p)&&((u=Vt(p))===p||!re(t,u)))&&(a?s&&(s[p]!==void 0||s[u]!==void 0)&&(o[p]=Fn(a,c,p,void 0,e,!0)):delete o[p]);if(i!==c)for(const p in i)(!t||!re(t,p))&&(delete i[p],d=!0)}d&&yt(e.attrs,"set","")}function ri(e,t,s,n){const[o,i]=e.propsOptions;let l=!1,c;if(t)for(let a in t){if(gs(a))continue;const d=t[a];let u;o&&re(o,u=Ze(a))?!i||!i.includes(u)?s[u]=d:(c||(c={}))[u]=d:hn(e.emitsOptions,a)||(!(a in n)||d!==n[a])&&(n[a]=d,l=!0)}if(i){const a=oe(s),d=c||fe;for(let u=0;u<i.length;u++){const p=i[u];s[p]=Fn(o,a,p,d[p],e,!re(d,p))}}return l}function Fn(e,t,s,n,o,i){const l=e[s];if(l!=null){const c=re(l,"default");if(c&&n===void 0){const a=l.default;if(l.type!==Function&&!l.skipFactory&&J(a)){const{propsDefaults:d}=o;if(s in d)n=d[s];else{const u=Is(o);n=d[s]=a.call(null,t),u()}}else n=a;o.ce&&o.ce._setProp(s,n)}l[0]&&(i&&!c?n=!1:l[1]&&(n===""||n===Vt(s))&&(n=!0))}return n}const Ql=new WeakMap;function ii(e,t,s=!1){const n=s?Ql:t.propsCache,o=n.get(e);if(o)return o;const i=e.props,l={},c=[];let a=!1;if(!J(e)){const u=p=>{a=!0;const[g,v]=ii(p,t,!0);Me(l,g),v&&c.push(...v)};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!a)return me(e)&&n.set(e,Yt),Yt;if(z(i))for(let u=0;u<i.length;u++){const p=Ze(i[u]);Ao(p)&&(l[p]=fe)}else if(i)for(const u in i){const p=Ze(u);if(Ao(p)){const g=i[u],v=l[p]=z(g)||J(g)?{type:g}:Me({},g),y=v.type;let w=!1,O=!0;if(z(y))for(let U=0;U<y.length;++U){const x=y[U],_=J(x)&&x.name;if(_==="Boolean"){w=!0;break}else _==="String"&&(O=!1)}else w=J(y)&&y.name==="Boolean";v[0]=w,v[1]=O,(w||re(v,"default"))&&c.push(p)}}const d=[l,c];return me(e)&&n.set(e,d),d}function Ao(e){return e[0]!=="$"&&!gs(e)}const uo=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",fo=e=>z(e)?e.map(pt):[pt(e)],Yl=(e,t,s)=>{if(t._n)return t;const n=pe((...o)=>fo(t(...o)),s);return n._c=!1,n},li=(e,t,s)=>{const n=e._ctx;for(const o in e){if(uo(o))continue;const i=e[o];if(J(i))t[o]=Yl(o,i,n);else if(i!=null){const l=fo(i);t[o]=()=>l}}},ai=(e,t)=>{const s=fo(t);e.slots.default=()=>s},ci=(e,t,s)=>{for(const n in t)(s||!uo(n))&&(e[n]=t[n])},Xl=(e,t,s)=>{const n=e.slots=ni();if(e.vnode.shapeFlag&32){const o=t.__;o&&On(n,"__",o,!0);const i=t._;i?(ci(n,t,s),s&&On(n,"_",i,!0)):li(t,n)}else t&&ai(e,t)},ea=(e,t,s)=>{const{vnode:n,slots:o}=e;let i=!0,l=fe;if(n.shapeFlag&32){const c=t._;c?s&&c===1?i=!1:ci(o,t,s):(i=!t.$stable,li(t,o)),l=t}else t&&(ai(e,t),l={default:1});if(i)for(const c in o)!uo(c)&&l[c]==null&&delete o[c]},Fe=ha;function ta(e){return sa(e)}function sa(e,t){const s=cn();s.__VUE__=!0;const{insert:n,remove:o,patchProp:i,createElement:l,createText:c,createComment:a,setText:d,setElementText:u,parentNode:p,nextSibling:g,setScopeId:v=ht,insertStaticContent:y}=e,w=(f,h,m,$=null,A=null,P=null,N=void 0,V=null,I=!!h.dynamicChildren)=>{if(f===h)return;f&&!ps(f,h)&&($=S(f),Ee(f,A,P,!0),f=null),h.patchFlag===-2&&(I=!1,h.dynamicChildren=null);const{type:T,ref:K,shapeFlag:B}=h;switch(T){case vn:O(f,h,m,$);break;case Ot:U(f,h,m,$);break;case zs:f==null&&x(h,m,$,N);break;case ae:Je(f,h,m,$,A,P,N,V,I);break;default:B&1?q(f,h,m,$,A,P,N,V,I):B&6?We(f,h,m,$,A,P,N,V,I):(B&64||B&128)&&T.process(f,h,m,$,A,P,N,V,I,b)}K!=null&&A?bs(K,f&&f.ref,P,h||f,!h):K==null&&f&&f.ref!=null&&bs(f.ref,null,P,f,!0)},O=(f,h,m,$)=>{if(f==null)n(h.el=c(h.children),m,$);else{const A=h.el=f.el;h.children!==f.children&&d(A,h.children)}},U=(f,h,m,$)=>{f==null?n(h.el=a(h.children||""),m,$):h.el=f.el},x=(f,h,m,$)=>{[f.el,f.anchor]=y(f.children,h,m,$,f.el,f.anchor)},_=({el:f,anchor:h},m,$)=>{let A;for(;f&&f!==h;)A=g(f),n(f,m,$),f=A;n(h,m,$)},j=({el:f,anchor:h})=>{let m;for(;f&&f!==h;)m=g(f),o(f),f=m;o(h)},q=(f,h,m,$,A,P,N,V,I)=>{h.type==="svg"?N="svg":h.type==="math"&&(N="mathml"),f==null?ue(h,m,$,A,P,N,V,I):C(f,h,A,P,N,V,I)},ue=(f,h,m,$,A,P,N,V)=>{let I,T;const{props:K,shapeFlag:B,transition:D,dirs:Z}=f;if(I=f.el=l(f.type,P,K&&K.is,K),B&8?u(I,f.children):B&16&&F(f.children,I,null,$,A,Sn(f,P),N,V),Z&&Nt(f,null,$,"created"),ie(I,f,f.scopeId,N,$),K){for(const ve in K)ve!=="value"&&!gs(ve)&&i(I,ve,null,K[ve],P,$);"value"in K&&i(I,"value",null,K.value,P),(T=K.onVnodeBeforeMount)&&dt(T,$,f)}Z&&Nt(f,null,$,"beforeMount");const ee=na(A,D);ee&&D.beforeEnter(I),n(I,h,m),((T=K&&K.onVnodeMounted)||ee||Z)&&Fe(()=>{T&&dt(T,$,f),ee&&D.enter(I),Z&&Nt(f,null,$,"mounted")},A)},ie=(f,h,m,$,A)=>{if(m&&v(f,m),$)for(let P=0;P<$.length;P++)v(f,$[P]);if(A){let P=A.subTree;if(h===P||vi(P.type)&&(P.ssContent===h||P.ssFallback===h)){const N=A.vnode;ie(f,N,N.scopeId,N.slotScopeIds,A.parent)}}},F=(f,h,m,$,A,P,N,V,I=0)=>{for(let T=I;T<f.length;T++){const K=f[T]=V?Rt(f[T]):pt(f[T]);w(null,K,h,m,$,A,P,N,V)}},C=(f,h,m,$,A,P,N)=>{const V=h.el=f.el;let{patchFlag:I,dynamicChildren:T,dirs:K}=h;I|=f.patchFlag&16;const B=f.props||fe,D=h.props||fe;let Z;if(m&&jt(m,!1),(Z=D.onVnodeBeforeUpdate)&&dt(Z,m,h,f),K&&Nt(h,f,m,"beforeUpdate"),m&&jt(m,!0),(B.innerHTML&&D.innerHTML==null||B.textContent&&D.textContent==null)&&u(V,""),T?be(f.dynamicChildren,T,V,m,$,Sn(h,A),P):N||se(f,h,V,null,m,$,Sn(h,A),P,!1),I>0){if(I&16)Q(V,B,D,m,A);else if(I&2&&B.class!==D.class&&i(V,"class",null,D.class,A),I&4&&i(V,"style",B.style,D.style,A),I&8){const ee=h.dynamicProps;for(let ve=0;ve<ee.length;ve++){const le=ee[ve],Ue=B[le],Ne=D[le];(Ne!==Ue||le==="value")&&i(V,le,Ue,Ne,A,m)}}I&1&&f.children!==h.children&&u(V,h.children)}else!N&&T==null&&Q(V,B,D,m,A);((Z=D.onVnodeUpdated)||K)&&Fe(()=>{Z&&dt(Z,m,h,f),K&&Nt(h,f,m,"updated")},$)},be=(f,h,m,$,A,P,N)=>{for(let V=0;V<h.length;V++){const I=f[V],T=h[V],K=I.el&&(I.type===ae||!ps(I,T)||I.shapeFlag&198)?p(I.el):m;w(I,T,K,null,$,A,P,N,!0)}},Q=(f,h,m,$,A)=>{if(h!==m){if(h!==fe)for(const P in h)!gs(P)&&!(P in m)&&i(f,P,h[P],null,A,$);for(const P in m){if(gs(P))continue;const N=m[P],V=h[P];N!==V&&P!=="value"&&i(f,P,V,N,A,$)}"value"in m&&i(f,"value",h.value,m.value,A)}},Je=(f,h,m,$,A,P,N,V,I)=>{const T=h.el=f?f.el:c(""),K=h.anchor=f?f.anchor:c("");let{patchFlag:B,dynamicChildren:D,slotScopeIds:Z}=h;Z&&(V=V?V.concat(Z):Z),f==null?(n(T,m,$),n(K,m,$),F(h.children||[],m,K,A,P,N,V,I)):B>0&&B&64&&D&&f.dynamicChildren?(be(f.dynamicChildren,D,m,A,P,N,V),(h.key!=null||A&&h===A.subTree)&&ui(f,h,!0)):se(f,h,m,K,A,P,N,V,I)},We=(f,h,m,$,A,P,N,V,I)=>{h.slotScopeIds=V,f==null?h.shapeFlag&512?A.ctx.activate(h,m,$,N,I):Ut(h,m,$,A,P,N,I):St(f,h,I)},Ut=(f,h,m,$,A,P,N)=>{const V=f.component=_a(f,$,A);if(Qr(f)&&(V.ctx.renderer=b),Ca(V,!1,N),V.asyncDep){if(A&&A.registerDep(V,_e,N),!f.el){const I=V.subTree=X(Ot);U(null,I,h,m),f.placeholder=I.el}}else _e(V,f,h,m,A,P,N)},St=(f,h,m)=>{const $=h.component=f.component;if(fa(f,h,m))if($.asyncDep&&!$.asyncResolved){te($,h,m);return}else $.next=h,$.update();else h.el=f.el,$.vnode=h},_e=(f,h,m,$,A,P,N)=>{const V=()=>{if(f.isMounted){let{next:B,bu:D,u:Z,parent:ee,vnode:ve}=f;{const ct=di(f);if(ct){B&&(B.el=ve.el,te(f,B,N)),ct.asyncDep.then(()=>{f.isUnmounted||V()});return}}let le=B,Ue;jt(f,!1),B?(B.el=ve.el,te(f,B,N)):B=ve,D&&Fs(D),(Ue=B.props&&B.props.onVnodeBeforeUpdate)&&dt(Ue,ee,B,ve),jt(f,!0);const Ne=To(f),at=f.subTree;f.subTree=Ne,w(at,Ne,p(at.el),S(at),f,A,P),B.el=Ne.el,le===null&&pa(f,Ne.el),Z&&Fe(Z,A),(Ue=B.props&&B.props.onVnodeUpdated)&&Fe(()=>dt(Ue,ee,B,ve),A)}else{let B;const{el:D,props:Z}=h,{bm:ee,m:ve,parent:le,root:Ue,type:Ne}=f,at=ys(h);jt(f,!1),ee&&Fs(ee),!at&&(B=Z&&Z.onVnodeBeforeMount)&&dt(B,le,h),jt(f,!0);{Ue.ce&&Ue.ce._def.shadowRoot!==!1&&Ue.ce._injectChildStyle(Ne);const ct=f.subTree=To(f);w(null,ct,m,$,f,A,P),h.el=ct.el}if(ve&&Fe(ve,A),!at&&(B=Z&&Z.onVnodeMounted)){const ct=h;Fe(()=>dt(B,le,ct),A)}(h.shapeFlag&256||le&&ys(le.vnode)&&le.vnode.shapeFlag&256)&&f.a&&Fe(f.a,A),f.isMounted=!0,h=m=$=null}};f.scope.on();const I=f.effect=new Sr(V);f.scope.off();const T=f.update=I.run.bind(I),K=f.job=I.runIfDirty.bind(I);K.i=f,K.id=f.uid,I.scheduler=()=>ao(K),jt(f,!0),T()},te=(f,h,m)=>{h.component=f;const $=f.vnode.props;f.vnode=h,f.next=null,Jl(f,h.props,$,m),ea(f,h.children,m),Ct(),$o(f),xt()},se=(f,h,m,$,A,P,N,V,I=!1)=>{const T=f&&f.children,K=f?f.shapeFlag:0,B=h.children,{patchFlag:D,shapeFlag:Z}=h;if(D>0){if(D&128){lt(T,B,m,$,A,P,N,V,I);return}else if(D&256){Qe(T,B,m,$,A,P,N,V,I);return}}Z&8?(K&16&&Le(T,A,P),B!==T&&u(m,B)):K&16?Z&16?lt(T,B,m,$,A,P,N,V,I):Le(T,A,P,!0):(K&8&&u(m,""),Z&16&&F(B,m,$,A,P,N,V,I))},Qe=(f,h,m,$,A,P,N,V,I)=>{f=f||Yt,h=h||Yt;const T=f.length,K=h.length,B=Math.min(T,K);let D;for(D=0;D<B;D++){const Z=h[D]=I?Rt(h[D]):pt(h[D]);w(f[D],Z,m,null,A,P,N,V,I)}T>K?Le(f,A,P,!0,!1,B):F(h,m,$,A,P,N,V,I,B)},lt=(f,h,m,$,A,P,N,V,I)=>{let T=0;const K=h.length;let B=f.length-1,D=K-1;for(;T<=B&&T<=D;){const Z=f[T],ee=h[T]=I?Rt(h[T]):pt(h[T]);if(ps(Z,ee))w(Z,ee,m,null,A,P,N,V,I);else break;T++}for(;T<=B&&T<=D;){const Z=f[B],ee=h[D]=I?Rt(h[D]):pt(h[D]);if(ps(Z,ee))w(Z,ee,m,null,A,P,N,V,I);else break;B--,D--}if(T>B){if(T<=D){const Z=D+1,ee=Z<K?h[Z].el:$;for(;T<=D;)w(null,h[T]=I?Rt(h[T]):pt(h[T]),m,ee,A,P,N,V,I),T++}}else if(T>D)for(;T<=B;)Ee(f[T],A,P,!0),T++;else{const Z=T,ee=T,ve=new Map;for(T=ee;T<=D;T++){const He=h[T]=I?Rt(h[T]):pt(h[T]);He.key!=null&&ve.set(He.key,T)}let le,Ue=0;const Ne=D-ee+1;let at=!1,ct=0;const ds=new Array(Ne);for(T=0;T<Ne;T++)ds[T]=0;for(T=Z;T<=B;T++){const He=f[T];if(Ue>=Ne){Ee(He,A,P,!0);continue}let ut;if(He.key!=null)ut=ve.get(He.key);else for(le=ee;le<=D;le++)if(ds[le-ee]===0&&ps(He,h[le])){ut=le;break}ut===void 0?Ee(He,A,P,!0):(ds[ut-ee]=T+1,ut>=ct?ct=ut:at=!0,w(He,h[ut],m,null,A,P,N,V,I),Ue++)}const wo=at?oa(ds):Yt;for(le=wo.length-1,T=Ne-1;T>=0;T--){const He=ee+T,ut=h[He],bo=h[He+1],yo=He+1<K?bo.el||bo.placeholder:$;ds[T]===0?w(null,ut,m,yo,A,P,N,V,I):at&&(le<0||T!==wo[le]?$e(ut,m,yo,2):le--)}}},$e=(f,h,m,$,A=null)=>{const{el:P,type:N,transition:V,children:I,shapeFlag:T}=f;if(T&6){$e(f.component.subTree,h,m,$);return}if(T&128){f.suspense.move(h,m,$);return}if(T&64){N.move(f,h,m,b);return}if(N===ae){n(P,h,m);for(let B=0;B<I.length;B++)$e(I[B],h,m,$);n(f.anchor,h,m);return}if(N===zs){_(f,h,m);return}if($!==2&&T&1&&V)if($===0)V.beforeEnter(P),n(P,h,m),Fe(()=>V.enter(P),A);else{const{leave:B,delayLeave:D,afterLeave:Z}=V,ee=()=>{f.ctx.isUnmounted?o(P):n(P,h,m)},ve=()=>{B(P,()=>{ee(),Z&&Z()})};D?D(P,ee,ve):ve()}else n(P,h,m)},Ee=(f,h,m,$=!1,A=!1)=>{const{type:P,props:N,ref:V,children:I,dynamicChildren:T,shapeFlag:K,patchFlag:B,dirs:D,cacheIndex:Z}=f;if(B===-2&&(A=!1),V!=null&&(Ct(),bs(V,null,m,f,!0),xt()),Z!=null&&(h.renderCache[Z]=void 0),K&256){h.ctx.deactivate(f);return}const ee=K&1&&D,ve=!ys(f);let le;if(ve&&(le=N&&N.onVnodeBeforeUnmount)&&dt(le,h,f),K&6)Gt(f.component,m,$);else{if(K&128){f.suspense.unmount(m,$);return}ee&&Nt(f,null,h,"beforeUnmount"),K&64?f.type.remove(f,h,m,b,$):T&&!T.hasOnce&&(P!==ae||B>0&&B&64)?Le(T,h,m,!1,!0):(P===ae&&B&384||!A&&K&16)&&Le(I,h,m),$&&Et(f)}(ve&&(le=N&&N.onVnodeUnmounted)||ee)&&Fe(()=>{le&&dt(le,h,f),ee&&Nt(f,null,h,"unmounted")},m)},Et=f=>{const{type:h,el:m,anchor:$,transition:A}=f;if(h===ae){Ye(m,$);return}if(h===zs){j(f);return}const P=()=>{o(m),A&&!A.persisted&&A.afterLeave&&A.afterLeave()};if(f.shapeFlag&1&&A&&!A.persisted){const{leave:N,delayLeave:V}=A,I=()=>N(m,P);V?V(f.el,P,I):I()}else P()},Ye=(f,h)=>{let m;for(;f!==h;)m=g(f),o(f),f=m;o(h)},Gt=(f,h,m)=>{const{bum:$,scope:A,job:P,subTree:N,um:V,m:I,a:T,parent:K,slots:{__:B}}=f;Mo(I),Mo(T),$&&Fs($),K&&z(B)&&B.forEach(D=>{K.renderCache[D]=void 0}),A.stop(),P&&(P.flags|=8,Ee(N,f,h,m)),V&&Fe(V,h),Fe(()=>{f.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Le=(f,h,m,$=!1,A=!1,P=0)=>{for(let N=P;N<f.length;N++)Ee(f[N],h,m,$,A)},S=f=>{if(f.shapeFlag&6)return S(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const h=g(f.anchor||f.el),m=h&&h[$l];return m?g(m):h};let H=!1;const k=(f,h,m)=>{f==null?h._vnode&&Ee(h._vnode,null,null,!0):w(h._vnode||null,f,h,null,null,null,m),h._vnode=f,H||(H=!0,$o(),qr(),H=!1)},b={p:w,um:Ee,m:$e,r:Et,mt:Ut,mc:F,pc:se,pbc:be,n:S,o:e};return{render:k,hydrate:void 0,createApp:Gl(k)}}function Sn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function jt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function na(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ui(e,t,s=!1){const n=e.children,o=t.children;if(z(n)&&z(o))for(let i=0;i<n.length;i++){const l=n[i];let c=o[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=o[i]=Rt(o[i]),c.el=l.el),!s&&c.patchFlag!==-2&&ui(l,c)),c.type===vn&&(c.el=l.el),c.type===Ot&&!c.el&&(c.el=l.el)}}function oa(e){const t=e.slice(),s=[0];let n,o,i,l,c;const a=e.length;for(n=0;n<a;n++){const d=e[n];if(d!==0){if(o=s[s.length-1],e[o]<d){t[n]=o,s.push(n);continue}for(i=0,l=s.length-1;i<l;)c=i+l>>1,e[s[c]]<d?i=c+1:l=c;d<e[s[i]]&&(i>0&&(t[n]=s[i-1]),s[i]=n)}}for(i=s.length,l=s[i-1];i-- >0;)s[i]=l,l=t[l];return s}function di(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:di(t)}function Mo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ra=Symbol.for("v-scx"),ia=()=>st(ra);function Ks(e,t,s){return fi(e,t,s)}function fi(e,t,s=fe){const{immediate:n,deep:o,flush:i,once:l}=s,c=Me({},s),a=t&&n||!t&&i!=="post";let d;if(Ps){if(i==="sync"){const v=ia();d=v.__watcherHandles||(v.__watcherHandles=[])}else if(!a){const v=()=>{};return v.stop=ht,v.resume=ht,v.pause=ht,v}}const u=Re;c.call=(v,y,w)=>gt(v,u,y,w);let p=!1;i==="post"?c.scheduler=v=>{Fe(v,u&&u.suspense)}:i!=="sync"&&(p=!0,c.scheduler=(v,y)=>{y?v():ao(v)}),c.augmentJob=v=>{t&&(v.flags|=4),p&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const g=_l(e,t,c);return Ps&&(d?d.push(g):a&&g()),g}function la(e,t,s){const n=this.proxy,o=ke(e)?e.includes(".")?pi(n,e):()=>n[e]:e.bind(n,n);let i;J(t)?i=t:(i=t.handler,s=t);const l=Is(this),c=fi(o,i.bind(n),s);return l(),c}function pi(e,t){const s=t.split(".");return()=>{let n=e;for(let o=0;o<s.length&&n;o++)n=n[s[o]];return n}}const aa=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ze(t)}Modifiers`]||e[`${Vt(t)}Modifiers`];function ca(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||fe;let o=s;const i=t.startsWith("update:"),l=i&&aa(n,t.slice(7));l&&(l.trim&&(o=s.map(u=>ke(u)?u.trim():u)),l.number&&(o=s.map(Qs)));let c,a=n[c=yn(t)]||n[c=yn(Ze(t))];!a&&i&&(a=n[c=yn(Vt(t))]),a&&gt(a,e,6,o);const d=n[c+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,gt(d,e,6,o)}}function hi(e,t,s=!1){const n=t.emitsCache,o=n.get(e);if(o!==void 0)return o;const i=e.emits;let l={},c=!1;if(!J(e)){const a=d=>{const u=hi(d,t,!0);u&&(c=!0,Me(l,u))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!c?(me(e)&&n.set(e,null),null):(z(i)?i.forEach(a=>l[a]=null):Me(l,i),me(e)&&n.set(e,l),l)}function hn(e,t){return!e||!rn(t)?!1:(t=t.slice(2).replace(/Once$/,""),re(e,t[0].toLowerCase()+t.slice(1))||re(e,Vt(t))||re(e,t))}function To(e){const{type:t,vnode:s,proxy:n,withProxy:o,propsOptions:[i],slots:l,attrs:c,emit:a,render:d,renderCache:u,props:p,data:g,setupState:v,ctx:y,inheritAttrs:w}=e,O=tn(e);let U,x;try{if(s.shapeFlag&4){const j=o||n,q=j;U=pt(d.call(q,j,u,p,v,g,y)),x=c}else{const j=t;U=pt(j.length>1?j(p,{attrs:c,slots:l,emit:a}):j(p,null)),x=t.props?c:ua(c)}}catch(j){ks.length=0,fn(j,e,1),U=X(Ot)}let _=U;if(x&&w!==!1){const j=Object.keys(x),{shapeFlag:q}=_;j.length&&q&7&&(i&&j.some(Jn)&&(x=da(x,i)),_=ns(_,x,!1,!0))}return s.dirs&&(_=ns(_,null,!1,!0),_.dirs=_.dirs?_.dirs.concat(s.dirs):s.dirs),s.transition&&co(_,s.transition),U=_,tn(O),U}const ua=e=>{let t;for(const s in e)(s==="class"||s==="style"||rn(s))&&((t||(t={}))[s]=e[s]);return t},da=(e,t)=>{const s={};for(const n in e)(!Jn(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function fa(e,t,s){const{props:n,children:o,component:i}=e,{props:l,children:c,patchFlag:a}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return n?Oo(n,l,d):!!l;if(a&8){const u=t.dynamicProps;for(let p=0;p<u.length;p++){const g=u[p];if(l[g]!==n[g]&&!hn(d,g))return!0}}}else return(o||c)&&(!c||!c.$stable)?!0:n===l?!1:n?l?Oo(n,l,d):!0:!!l;return!1}function Oo(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let o=0;o<n.length;o++){const i=n[o];if(t[i]!==e[i]&&!hn(s,i))return!0}return!1}function pa({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const vi=e=>e.__isSuspense;function ha(e,t){t&&t.pendingBranch?z(e)?t.effects.push(...e):t.effects.push(e):xl(e)}const ae=Symbol.for("v-fgt"),vn=Symbol.for("v-txt"),Ot=Symbol.for("v-cmt"),zs=Symbol.for("v-stc"),ks=[];let Ke=null;function E(e=!1){ks.push(Ke=e?null:[])}function va(){ks.pop(),Ke=ks[ks.length-1]||null}let Ls=1;function Io(e,t=!1){Ls+=e,e<0&&Ke&&t&&(Ke.hasOnce=!0)}function gi(e){return e.dynamicChildren=Ls>0?Ke||Yt:null,va(),Ls>0&&Ke&&Ke.push(e),e}function R(e,t,s,n,o,i){return gi(r(e,t,s,n,o,i,!0))}function Kt(e,t,s,n,o){return gi(X(e,t,s,n,o,!0))}function nn(e){return e?e.__v_isVNode===!0:!1}function ps(e,t){return e.type===t.type&&e.key===t.key}const mi=({key:e})=>e??null,Ws=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ke(e)||Ae(e)||J(e)?{i:De,r:e,k:t,f:!!s}:e:null);function r(e,t=null,s=null,n=0,o=null,i=e===ae?0:1,l=!1,c=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&mi(t),ref:t&&Ws(t),scopeId:Zr,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:De};return c?(po(a,s),i&128&&e.normalize(a)):s&&(a.shapeFlag|=ke(s)?8:16),Ls>0&&!l&&Ke&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&Ke.push(a),a}const X=ga;function ga(e,t=null,s=null,n=0,o=null,i=!1){if((!e||e===jl)&&(e=Ot),nn(e)){const c=ns(e,t,!0);return s&&po(c,s),Ls>0&&!i&&Ke&&(c.shapeFlag&6?Ke[Ke.indexOf(e)]=c:Ke.push(c)),c.patchFlag=-2,c}if(La(e)&&(e=e.__vccOpts),t){t=ma(t);let{class:c,style:a}=t;c&&!ke(c)&&(t.class=ce(c)),me(a)&&(io(a)&&!z(a)&&(a=Me({},a)),t.style=un(a))}const l=ke(e)?1:vi(e)?128:Sl(e)?64:me(e)?4:J(e)?2:0;return r(e,t,s,n,o,l,i,!0)}function ma(e){return e?io(e)||oi(e)?Me({},e):e:null}function ns(e,t,s=!1,n=!1){const{props:o,ref:i,patchFlag:l,children:c,transition:a}=e,d=t?wa(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&mi(d),ref:t&&t.ref?s&&i?z(i)?i.concat(Ws(t)):[i,Ws(t)]:Ws(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ae?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ns(e.ssContent),ssFallback:e.ssFallback&&ns(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&n&&co(u,a.clone(u)),u}function G(e=" ",t=0){return X(vn,null,e,t)}function he(e,t){const s=X(zs,null,e);return s.staticCount=t,s}function Y(e="",t=!1){return t?(E(),Kt(Ot,null,e)):X(Ot,null,e)}function pt(e){return e==null||typeof e=="boolean"?X(Ot):z(e)?X(ae,null,e.slice()):nn(e)?Rt(e):X(vn,null,String(e))}function Rt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ns(e)}function po(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(z(t))s=16;else if(typeof t=="object")if(n&65){const o=t.default;o&&(o._c&&(o._d=!1),po(e,o()),o._c&&(o._d=!0));return}else{s=32;const o=t._;!o&&!oi(t)?t._ctx=De:o===3&&De&&(De.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else J(t)?(t={default:t,_ctx:De},s=32):(t=String(t),n&64?(s=16,t=[G(t)]):s=8);e.children=t,e.shapeFlag|=s}function wa(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const o in n)if(o==="class")t.class!==n.class&&(t.class=ce([t.class,n.class]));else if(o==="style")t.style=un([t.style,n.style]);else if(rn(o)){const i=t[o],l=n[o];l&&i!==l&&!(z(i)&&i.includes(l))&&(t[o]=i?[].concat(i,l):l)}else o!==""&&(t[o]=n[o])}return t}function dt(e,t,s,n=null){gt(e,t,7,[s,n])}const ba=ti();let ya=0;function _a(e,t,s){const n=e.type,o=(t?t.appContext:e.appContext)||ba,i={uid:ya++,vnode:e,type:n,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new $r(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ii(n,o),emitsOptions:hi(n,o),emit:null,emitted:null,propsDefaults:fe,inheritAttrs:n.inheritAttrs,ctx:fe,data:fe,props:fe,attrs:fe,slots:fe,refs:fe,setupState:fe,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=ca.bind(null,i),e.ce&&e.ce(i),i}let Re=null;const ka=()=>Re||De;let on,Dn;{const e=cn(),t=(s,n)=>{let o;return(o=e[s])||(o=e[s]=[]),o.push(n),i=>{o.length>1?o.forEach(l=>l(i)):o[0](i)}};on=t("__VUE_INSTANCE_SETTERS__",s=>Re=s),Dn=t("__VUE_SSR_SETTERS__",s=>Ps=s)}const Is=e=>{const t=Re;return on(e),e.scope.on(),()=>{e.scope.off(),on(t)}},Vo=()=>{Re&&Re.scope.off(),on(null)};function wi(e){return e.vnode.shapeFlag&4}let Ps=!1;function Ca(e,t=!1,s=!1){t&&Dn(t);const{props:n,children:o}=e.vnode,i=wi(e);Zl(e,n,i,t),Xl(e,o,s||t);const l=i?xa(e,t):void 0;return t&&Dn(!1),l}function xa(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Hl);const{setup:n}=s;if(n){Ct();const o=e.setupContext=n.length>1?Sa(e):null,i=Is(e),l=Ts(n,e,0,[e.props,o]),c=br(l);if(xt(),i(),(c||e.sp)&&!ys(e)&&Jr(e),c){if(l.then(Vo,Vo),t)return l.then(a=>{Uo(e,a)}).catch(a=>{fn(a,e,0)});e.asyncDep=l}else Uo(e,l)}else bi(e)}function Uo(e,t,s){J(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:me(t)&&(e.setupState=Kr(t)),bi(e)}function bi(e,t,s){const n=e.type;e.render||(e.render=n.render||ht);{const o=Is(e);Ct();try{Fl(e)}finally{xt(),o()}}}const $a={get(e,t){return Pe(e,"get",""),e[t]}};function Sa(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,$a),slots:e.slots,emit:e.emit,expose:t}}function gn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Kr(Fr(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in _s)return _s[s](e)},has(t,s){return s in t||s in _s}})):e.proxy}function Ea(e,t=!0){return J(e)?e.displayName||e.name:e.name||t&&e.__name}function La(e){return J(e)&&"__vccOpts"in e}const we=(e,t)=>bl(e,t,Ps);function yi(e,t,s){const n=arguments.length;return n===2?me(t)&&!z(t)?nn(t)?X(e,null,[t]):X(e,t):X(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&nn(s)&&(s=[s]),X(e,t,s))}const Pa="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Kn;const No=typeof window<"u"&&window.trustedTypes;if(No)try{Kn=No.createPolicy("vue",{createHTML:e=>e})}catch{}const _i=Kn?e=>Kn.createHTML(e):e=>e,Ra="http://www.w3.org/2000/svg",Aa="http://www.w3.org/1998/Math/MathML",bt=typeof document<"u"?document:null,jo=bt&&bt.createElement("template"),Ma={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const o=t==="svg"?bt.createElementNS(Ra,e):t==="mathml"?bt.createElementNS(Aa,e):s?bt.createElement(e,{is:s}):bt.createElement(e);return e==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:e=>bt.createTextNode(e),createComment:e=>bt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>bt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,o,i){const l=s?s.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),s),!(o===i||!(o=o.nextSibling)););else{jo.innerHTML=_i(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const c=jo.content;if(n==="svg"||n==="mathml"){const a=c.firstChild;for(;a.firstChild;)c.appendChild(a.firstChild);c.removeChild(a)}t.insertBefore(c,s)}return[l?l.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Ta=Symbol("_vtc");function Oa(e,t,s){const n=e[Ta];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Bo=Symbol("_vod"),Ia=Symbol("_vsh"),Va=Symbol(""),Ua=/(^|;)\s*display\s*:/;function Na(e,t,s){const n=e.style,o=ke(s);let i=!1;if(s&&!o){if(t)if(ke(t))for(const l of t.split(";")){const c=l.slice(0,l.indexOf(":")).trim();s[c]==null&&qs(n,c,"")}else for(const l in t)s[l]==null&&qs(n,l,"");for(const l in s)l==="display"&&(i=!0),qs(n,l,s[l])}else if(o){if(t!==s){const l=n[Va];l&&(s+=";"+l),n.cssText=s,i=Ua.test(s)}}else t&&e.removeAttribute("style");Bo in e&&(e[Bo]=i?n.display:"",e[Ia]&&(n.display="none"))}const Ho=/\s*!important$/;function qs(e,t,s){if(z(s))s.forEach(n=>qs(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=ja(e,t);Ho.test(s)?e.setProperty(Vt(n),s.replace(Ho,""),"important"):e[n]=s}}const Fo=["Webkit","Moz","ms"],En={};function ja(e,t){const s=En[t];if(s)return s;let n=Ze(t);if(n!=="filter"&&n in e)return En[t]=n;n=an(n);for(let o=0;o<Fo.length;o++){const i=Fo[o]+n;if(i in e)return En[t]=i}return t}const Do="http://www.w3.org/1999/xlink";function Ko(e,t,s,n,o,i=qi(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Do,t.slice(6,t.length)):e.setAttributeNS(Do,t,s):s==null||i&&!kr(s)?e.removeAttribute(t):e.setAttribute(t,i?"":vt(s)?String(s):s)}function zo(e,t,s,n,o){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?_i(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(c!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let l=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=kr(s):s==null&&c==="string"?(s="",l=!0):c==="number"&&(s=0,l=!0)}try{e[t]=s}catch{}l&&e.removeAttribute(o||t)}function kt(e,t,s,n){e.addEventListener(t,s,n)}function Ba(e,t,s,n){e.removeEventListener(t,s,n)}const Wo=Symbol("_vei");function Ha(e,t,s,n,o=null){const i=e[Wo]||(e[Wo]={}),l=i[t];if(n&&l)l.value=n;else{const[c,a]=Fa(t);if(n){const d=i[t]=za(n,o);kt(e,c,d,a)}else l&&(Ba(e,c,l,a),i[t]=void 0)}}const qo=/(?:Once|Passive|Capture)$/;function Fa(e){let t;if(qo.test(e)){t={};let n;for(;n=e.match(qo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Vt(e.slice(2)),t]}let Ln=0;const Da=Promise.resolve(),Ka=()=>Ln||(Da.then(()=>Ln=0),Ln=Date.now());function za(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;gt(Wa(n,s.value),t,5,[n])};return s.value=e,s.attached=Ka(),s}function Wa(e,t){if(z(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>o=>!o._stopped&&n&&n(o))}else return t}const Go=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,qa=(e,t,s,n,o,i)=>{const l=o==="svg";t==="class"?Oa(e,n,l):t==="style"?Na(e,s,n):rn(t)?Jn(t)||Ha(e,t,s,n,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ga(e,t,n,l))?(zo(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ko(e,t,n,l,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ke(n))?zo(e,Ze(t),n,i,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Ko(e,t,n,l))};function Ga(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&Go(t)&&J(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Go(t)&&ke(s)?!1:t in e}const It=e=>{const t=e.props["onUpdate:modelValue"]||!1;return z(t)?s=>Fs(t,s):t};function Za(e){e.target.composing=!0}function Zo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ge=Symbol("_assign"),Ce={created(e,{modifiers:{lazy:t,trim:s,number:n}},o){e[Ge]=It(o);const i=n||o.props&&o.props.type==="number";kt(e,t?"change":"input",l=>{if(l.target.composing)return;let c=e.value;s&&(c=c.trim()),i&&(c=Qs(c)),e[Ge](c)}),s&&kt(e,"change",()=>{e.value=e.value.trim()}),t||(kt(e,"compositionstart",Za),kt(e,"compositionend",Zo),kt(e,"change",Zo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:o,number:i}},l){if(e[Ge]=It(l),e.composing)return;const c=(i||e.type==="number")&&!/^0\d/.test(e.value)?Qs(e.value):e.value,a=t??"";c!==a&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||o&&e.value.trim()===a)||(e.value=a))}},ho={deep:!0,created(e,t,s){e[Ge]=It(s),kt(e,"change",()=>{const n=e._modelValue,o=os(e),i=e.checked,l=e[Ge];if(z(n)){const c=Xn(n,o),a=c!==-1;if(i&&!a)l(n.concat(o));else if(!i&&a){const d=[...n];d.splice(c,1),l(d)}}else if(cs(n)){const c=new Set(n);i?c.add(o):c.delete(o),l(c)}else l(ki(e,i))})},mounted:Jo,beforeUpdate(e,t,s){e[Ge]=It(s),Jo(e,t,s)}};function Jo(e,{value:t,oldValue:s},n){e._modelValue=t;let o;if(z(t))o=Xn(t,n.props.value)>-1;else if(cs(t))o=t.has(n.props.value);else{if(t===s)return;o=Dt(t,ki(e,!0))}e.checked!==o&&(e.checked=o)}const Ja={created(e,{value:t},s){e.checked=Dt(t,s.props.value),e[Ge]=It(s),kt(e,"change",()=>{e[Ge](os(e))})},beforeUpdate(e,{value:t,oldValue:s},n){e[Ge]=It(n),t!==s&&(e.checked=Dt(t,n.props.value))}},Gs={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const o=cs(t);kt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,l=>l.selected).map(l=>s?Qs(os(l)):os(l));e[Ge](e.multiple?o?new Set(i):i:i[0]),e._assigning=!0,lo(()=>{e._assigning=!1})}),e[Ge]=It(n)},mounted(e,{value:t}){Qo(e,t)},beforeUpdate(e,t,s){e[Ge]=It(s)},updated(e,{value:t}){e._assigning||Qo(e,t)}};function Qo(e,t){const s=e.multiple,n=z(t);if(!(s&&!n&&!cs(t))){for(let o=0,i=e.options.length;o<i;o++){const l=e.options[o],c=os(l);if(s)if(n){const a=typeof c;a==="string"||a==="number"?l.selected=t.some(d=>String(d)===String(c)):l.selected=Xn(t,c)>-1}else l.selected=t.has(c);else if(Dt(os(l),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function os(e){return"_value"in e?e._value:e.value}function ki(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const rs={created(e,t,s){Bs(e,t,s,null,"created")},mounted(e,t,s){Bs(e,t,s,null,"mounted")},beforeUpdate(e,t,s,n){Bs(e,t,s,n,"beforeUpdate")},updated(e,t,s,n){Bs(e,t,s,n,"updated")}};function Qa(e,t){switch(e){case"SELECT":return Gs;case"TEXTAREA":return Ce;default:switch(t){case"checkbox":return ho;case"radio":return Ja;default:return Ce}}}function Bs(e,t,s,n,o){const l=Qa(e.tagName,s.props&&s.props.type)[o];l&&l(e,t,s,n)}const Ya=["ctrl","shift","alt","meta"],Xa={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Ya.some(s=>e[`${s}Key`]&&!t.includes(s))},is=(e,t)=>{const s=e._withMods||(e._withMods={}),n=t.join(".");return s[n]||(s[n]=(o,...i)=>{for(let l=0;l<t.length;l++){const c=Xa[t[l]];if(c&&c(o,t))return}return e(o,...i)})},ec={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Yo=(e,t)=>{const s=e._withKeys||(e._withKeys={}),n=t.join(".");return s[n]||(s[n]=o=>{if(!("key"in o))return;const i=Vt(o.key);if(t.some(l=>l===i||ec[l]===i))return e(o)})},tc=Me({patchProp:qa},Ma);let Xo;function sc(){return Xo||(Xo=ta(tc))}const nc=(...e)=>{const t=sc().createApp(...e),{mount:s}=t;return t.mount=n=>{const o=rc(n);if(!o)return;const i=t._component;!J(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const l=s(o,!1,oc(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),l},t};function oc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function rc(e){return ke(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ic=Symbol();var er;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(er||(er={}));function lc(){const e=Zi(!0),t=e.run(()=>W({}));let s=[],n=[];const o=Fr({install(i){o._a=i,i.provide(ic,o),i.config.globalProperties.$pinia=o,n.forEach(l=>s.push(l)),n=[]},use(i){return this._a?s.push(i):n.push(i),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return o}const ze=(e,t)=>{const s=e.__vccOpts||e;for(const[n,o]of t)s[n]=o;return s},ac={},cc={id:"app"};function uc(e,t){const s=ot("router-view");return E(),R("div",cc,[X(s)])}const dc=ze(ac,[["render",uc]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Qt=typeof document<"u";function Ci(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function fc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Ci(e.default)}const ne=Object.assign;function Pn(e,t){const s={};for(const n in t){const o=t[n];s[n]=nt(o)?o.map(e):e(o)}return s}const Cs=()=>{},nt=Array.isArray,xi=/#/g,pc=/&/g,hc=/\//g,vc=/=/g,gc=/\?/g,$i=/\+/g,mc=/%5B/g,wc=/%5D/g,Si=/%5E/g,bc=/%60/g,Ei=/%7B/g,yc=/%7C/g,Li=/%7D/g,_c=/%20/g;function vo(e){return encodeURI(""+e).replace(yc,"|").replace(mc,"[").replace(wc,"]")}function kc(e){return vo(e).replace(Ei,"{").replace(Li,"}").replace(Si,"^")}function zn(e){return vo(e).replace($i,"%2B").replace(_c,"+").replace(xi,"%23").replace(pc,"%26").replace(bc,"`").replace(Ei,"{").replace(Li,"}").replace(Si,"^")}function Cc(e){return zn(e).replace(vc,"%3D")}function xc(e){return vo(e).replace(xi,"%23").replace(gc,"%3F")}function $c(e){return e==null?"":xc(e).replace(hc,"%2F")}function Rs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Sc=/\/$/,Ec=e=>e.replace(Sc,"");function Rn(e,t,s="/"){let n,o={},i="",l="";const c=t.indexOf("#");let a=t.indexOf("?");return c<a&&c>=0&&(a=-1),a>-1&&(n=t.slice(0,a),i=t.slice(a+1,c>-1?c:t.length),o=e(i)),c>-1&&(n=n||t.slice(0,c),l=t.slice(c,t.length)),n=Ac(n??t,s),{fullPath:n+(i&&"?")+i+l,path:n,query:o,hash:Rs(l)}}function Lc(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function tr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Pc(e,t,s){const n=t.matched.length-1,o=s.matched.length-1;return n>-1&&n===o&&ls(t.matched[n],s.matched[o])&&Pi(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function ls(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Pi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Rc(e[s],t[s]))return!1;return!0}function Rc(e,t){return nt(e)?sr(e,t):nt(t)?sr(t,e):e===t}function sr(e,t){return nt(t)?e.length===t.length&&e.every((s,n)=>s===t[n]):e.length===1&&e[0]===t}function Ac(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),n=e.split("/"),o=n[n.length-1];(o===".."||o===".")&&n.push("");let i=s.length-1,l,c;for(l=0;l<n.length;l++)if(c=n[l],c!==".")if(c==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+n.slice(l).join("/")}const Lt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var As;(function(e){e.pop="pop",e.push="push"})(As||(As={}));var xs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(xs||(xs={}));function Mc(e){if(!e)if(Qt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ec(e)}const Tc=/^[^#]+#/;function Oc(e,t){return e.replace(Tc,"#")+t}function Ic(e,t){const s=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-s.left-(t.left||0),top:n.top-s.top-(t.top||0)}}const mn=()=>({left:window.scrollX,top:window.scrollY});function Vc(e){let t;if("el"in e){const s=e.el,n=typeof s=="string"&&s.startsWith("#"),o=typeof s=="string"?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o)return;t=Ic(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function nr(e,t){return(history.state?history.state.position-t:-1)+e}const Wn=new Map;function Uc(e,t){Wn.set(e,t)}function Nc(e){const t=Wn.get(e);return Wn.delete(e),t}let jc=()=>location.protocol+"//"+location.host;function Ri(e,t){const{pathname:s,search:n,hash:o}=t,i=e.indexOf("#");if(i>-1){let c=o.includes(e.slice(i))?e.slice(i).length:1,a=o.slice(c);return a[0]!=="/"&&(a="/"+a),tr(a,"")}return tr(s,e)+n+o}function Bc(e,t,s,n){let o=[],i=[],l=null;const c=({state:g})=>{const v=Ri(e,location),y=s.value,w=t.value;let O=0;if(g){if(s.value=v,t.value=g,l&&l===y){l=null;return}O=w?g.position-w.position:0}else n(v);o.forEach(U=>{U(s.value,y,{delta:O,type:As.pop,direction:O?O>0?xs.forward:xs.back:xs.unknown})})};function a(){l=s.value}function d(g){o.push(g);const v=()=>{const y=o.indexOf(g);y>-1&&o.splice(y,1)};return i.push(v),v}function u(){const{history:g}=window;g.state&&g.replaceState(ne({},g.state,{scroll:mn()}),"")}function p(){for(const g of i)g();i=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:a,listen:d,destroy:p}}function or(e,t,s,n=!1,o=!1){return{back:e,current:t,forward:s,replaced:n,position:window.history.length,scroll:o?mn():null}}function Hc(e){const{history:t,location:s}=window,n={value:Ri(e,s)},o={value:t.state};o.value||i(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(a,d,u){const p=e.indexOf("#"),g=p>-1?(s.host&&document.querySelector("base")?e:e.slice(p))+a:jc()+e+a;try{t[u?"replaceState":"pushState"](d,"",g),o.value=d}catch(v){console.error(v),s[u?"replace":"assign"](g)}}function l(a,d){const u=ne({},t.state,or(o.value.back,a,o.value.forward,!0),d,{position:o.value.position});i(a,u,!0),n.value=a}function c(a,d){const u=ne({},o.value,t.state,{forward:a,scroll:mn()});i(u.current,u,!0);const p=ne({},or(n.value,a,null),{position:u.position+1},d);i(a,p,!1),n.value=a}return{location:n,state:o,push:c,replace:l}}function Fc(e){e=Mc(e);const t=Hc(e),s=Bc(e,t.state,t.location,t.replace);function n(i,l=!0){l||s.pauseListeners(),history.go(i)}const o=ne({location:"",base:e,go:n,createHref:Oc.bind(null,e)},t,s);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Dc(e){return typeof e=="string"||e&&typeof e=="object"}function Ai(e){return typeof e=="string"||typeof e=="symbol"}const Mi=Symbol("");var rr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(rr||(rr={}));function as(e,t){return ne(new Error,{type:e,[Mi]:!0},t)}function wt(e,t){return e instanceof Error&&Mi in e&&(t==null||!!(e.type&t))}const ir="[^/]+?",Kc={sensitive:!1,strict:!1,start:!0,end:!0},zc=/[.+*?^${}()[\]/\\]/g;function Wc(e,t){const s=ne({},Kc,t),n=[];let o=s.start?"^":"";const i=[];for(const d of e){const u=d.length?[]:[90];s.strict&&!d.length&&(o+="/");for(let p=0;p<d.length;p++){const g=d[p];let v=40+(s.sensitive?.25:0);if(g.type===0)p||(o+="/"),o+=g.value.replace(zc,"\\$&"),v+=40;else if(g.type===1){const{value:y,repeatable:w,optional:O,regexp:U}=g;i.push({name:y,repeatable:w,optional:O});const x=U||ir;if(x!==ir){v+=10;try{new RegExp(`(${x})`)}catch(j){throw new Error(`Invalid custom RegExp for param "${y}" (${x}): `+j.message)}}let _=w?`((?:${x})(?:/(?:${x}))*)`:`(${x})`;p||(_=O&&d.length<2?`(?:/${_})`:"/"+_),O&&(_+="?"),o+=_,v+=20,O&&(v+=-8),w&&(v+=-20),x===".*"&&(v+=-50)}u.push(v)}n.push(u)}if(s.strict&&s.end){const d=n.length-1;n[d][n[d].length-1]+=.7000000000000001}s.strict||(o+="/?"),s.end?o+="$":s.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,s.sensitive?"":"i");function c(d){const u=d.match(l),p={};if(!u)return null;for(let g=1;g<u.length;g++){const v=u[g]||"",y=i[g-1];p[y.name]=v&&y.repeatable?v.split("/"):v}return p}function a(d){let u="",p=!1;for(const g of e){(!p||!u.endsWith("/"))&&(u+="/"),p=!1;for(const v of g)if(v.type===0)u+=v.value;else if(v.type===1){const{value:y,repeatable:w,optional:O}=v,U=y in d?d[y]:"";if(nt(U)&&!w)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const x=nt(U)?U.join("/"):U;if(!x)if(O)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):p=!0);else throw new Error(`Missing required param "${y}"`);u+=x}}return u||"/"}return{re:l,score:n,keys:i,parse:c,stringify:a}}function qc(e,t){let s=0;for(;s<e.length&&s<t.length;){const n=t[s]-e[s];if(n)return n;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ti(e,t){let s=0;const n=e.score,o=t.score;for(;s<n.length&&s<o.length;){const i=qc(n[s],o[s]);if(i)return i;s++}if(Math.abs(o.length-n.length)===1){if(lr(n))return 1;if(lr(o))return-1}return o.length-n.length}function lr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Gc={type:0,value:""},Zc=/[a-zA-Z0-9_]/;function Jc(e){if(!e)return[[]];if(e==="/")return[[Gc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${s})/"${d}": ${v}`)}let s=0,n=s;const o=[];let i;function l(){i&&o.push(i),i=[]}let c=0,a,d="",u="";function p(){d&&(s===0?i.push({type:0,value:d}):s===1||s===2||s===3?(i.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:u,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=a}for(;c<e.length;){if(a=e[c++],a==="\\"&&s!==2){n=s,s=4;continue}switch(s){case 0:a==="/"?(d&&p(),l()):a===":"?(p(),s=1):g();break;case 4:g(),s=n;break;case 1:a==="("?s=2:Zc.test(a)?g():(p(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&c--);break;case 2:a===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+a:s=3:u+=a;break;case 3:p(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&c--,u="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${d}"`),p(),l(),o}function Qc(e,t,s){const n=Wc(Jc(e.path),s),o=ne(n,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Yc(e,t){const s=[],n=new Map;t=dr({strict:!1,end:!0,sensitive:!1},t);function o(p){return n.get(p)}function i(p,g,v){const y=!v,w=cr(p);w.aliasOf=v&&v.record;const O=dr(t,p),U=[w];if("alias"in p){const j=typeof p.alias=="string"?[p.alias]:p.alias;for(const q of j)U.push(cr(ne({},w,{components:v?v.record.components:w.components,path:q,aliasOf:v?v.record:w})))}let x,_;for(const j of U){const{path:q}=j;if(g&&q[0]!=="/"){const ue=g.record.path,ie=ue[ue.length-1]==="/"?"":"/";j.path=g.record.path+(q&&ie+q)}if(x=Qc(j,g,O),v?v.alias.push(x):(_=_||x,_!==x&&_.alias.push(x),y&&p.name&&!ur(x)&&l(p.name)),Oi(x)&&a(x),w.children){const ue=w.children;for(let ie=0;ie<ue.length;ie++)i(ue[ie],x,v&&v.children[ie])}v=v||x}return _?()=>{l(_)}:Cs}function l(p){if(Ai(p)){const g=n.get(p);g&&(n.delete(p),s.splice(s.indexOf(g),1),g.children.forEach(l),g.alias.forEach(l))}else{const g=s.indexOf(p);g>-1&&(s.splice(g,1),p.record.name&&n.delete(p.record.name),p.children.forEach(l),p.alias.forEach(l))}}function c(){return s}function a(p){const g=tu(p,s);s.splice(g,0,p),p.record.name&&!ur(p)&&n.set(p.record.name,p)}function d(p,g){let v,y={},w,O;if("name"in p&&p.name){if(v=n.get(p.name),!v)throw as(1,{location:p});O=v.record.name,y=ne(ar(g.params,v.keys.filter(_=>!_.optional).concat(v.parent?v.parent.keys.filter(_=>_.optional):[]).map(_=>_.name)),p.params&&ar(p.params,v.keys.map(_=>_.name))),w=v.stringify(y)}else if(p.path!=null)w=p.path,v=s.find(_=>_.re.test(w)),v&&(y=v.parse(w),O=v.record.name);else{if(v=g.name?n.get(g.name):s.find(_=>_.re.test(g.path)),!v)throw as(1,{location:p,currentLocation:g});O=v.record.name,y=ne({},g.params,p.params),w=v.stringify(y)}const U=[];let x=v;for(;x;)U.unshift(x.record),x=x.parent;return{name:O,path:w,params:y,matched:U,meta:eu(U)}}e.forEach(p=>i(p));function u(){s.length=0,n.clear()}return{addRoute:i,resolve:d,removeRoute:l,clearRoutes:u,getRoutes:c,getRecordMatcher:o}}function ar(e,t){const s={};for(const n of t)n in e&&(s[n]=e[n]);return s}function cr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Xc(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Xc(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const n in e.components)t[n]=typeof s=="object"?s[n]:s;return t}function ur(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function eu(e){return e.reduce((t,s)=>ne(t,s.meta),{})}function dr(e,t){const s={};for(const n in e)s[n]=n in t?t[n]:e[n];return s}function tu(e,t){let s=0,n=t.length;for(;s!==n;){const i=s+n>>1;Ti(e,t[i])<0?n=i:s=i+1}const o=su(e);return o&&(n=t.lastIndexOf(o,n-1)),n}function su(e){let t=e;for(;t=t.parent;)if(Oi(t)&&Ti(e,t)===0)return t}function Oi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function nu(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const i=n[o].replace($i," "),l=i.indexOf("="),c=Rs(l<0?i:i.slice(0,l)),a=l<0?null:Rs(i.slice(l+1));if(c in t){let d=t[c];nt(d)||(d=t[c]=[d]),d.push(a)}else t[c]=a}return t}function fr(e){let t="";for(let s in e){const n=e[s];if(s=Cc(s),n==null){n!==void 0&&(t+=(t.length?"&":"")+s);continue}(nt(n)?n.map(i=>i&&zn(i)):[n&&zn(n)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function ou(e){const t={};for(const s in e){const n=e[s];n!==void 0&&(t[s]=nt(n)?n.map(o=>o==null?null:""+o):n==null?n:""+n)}return t}const ru=Symbol(""),pr=Symbol(""),wn=Symbol(""),go=Symbol(""),qn=Symbol("");function hs(){let e=[];function t(n){return e.push(n),()=>{const o=e.indexOf(n);o>-1&&e.splice(o,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function At(e,t,s,n,o,i=l=>l()){const l=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return()=>new Promise((c,a)=>{const d=g=>{g===!1?a(as(4,{from:s,to:t})):g instanceof Error?a(g):Dc(g)?a(as(2,{from:t,to:g})):(l&&n.enterCallbacks[o]===l&&typeof g=="function"&&l.push(g),c())},u=i(()=>e.call(n&&n.instances[o],t,s,d));let p=Promise.resolve(u);e.length<3&&(p=p.then(d)),p.catch(g=>a(g))})}function An(e,t,s,n,o=i=>i()){const i=[];for(const l of e)for(const c in l.components){let a=l.components[c];if(!(t!=="beforeRouteEnter"&&!l.instances[c]))if(Ci(a)){const u=(a.__vccOpts||a)[t];u&&i.push(At(u,s,n,l,c,o))}else{let d=a();i.push(()=>d.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${c}" at "${l.path}"`);const p=fc(u)?u.default:u;l.mods[c]=u,l.components[c]=p;const v=(p.__vccOpts||p)[t];return v&&At(v,s,n,l,c,o)()}))}}return i}function hr(e){const t=st(wn),s=st(go),n=we(()=>{const a=ye(e.to);return t.resolve(a)}),o=we(()=>{const{matched:a}=n.value,{length:d}=a,u=a[d-1],p=s.matched;if(!u||!p.length)return-1;const g=p.findIndex(ls.bind(null,u));if(g>-1)return g;const v=vr(a[d-2]);return d>1&&vr(u)===v&&p[p.length-1].path!==v?p.findIndex(ls.bind(null,a[d-2])):g}),i=we(()=>o.value>-1&&uu(s.params,n.value.params)),l=we(()=>o.value>-1&&o.value===s.matched.length-1&&Pi(s.params,n.value.params));function c(a={}){if(cu(a)){const d=t[ye(e.replace)?"replace":"push"](ye(e.to)).catch(Cs);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:n,href:we(()=>n.value.href),isActive:i,isExactActive:l,navigate:c}}function iu(e){return e.length===1?e[0]:e}const lu=Be({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:hr,setup(e,{slots:t}){const s=xe(hr(e)),{options:n}=st(wn),o=we(()=>({[gr(e.activeClass,n.linkActiveClass,"router-link-active")]:s.isActive,[gr(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&iu(t.default(s));return e.custom?i:yi("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},i)}}}),au=lu;function cu(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function uu(e,t){for(const s in t){const n=t[s],o=e[s];if(typeof n=="string"){if(n!==o)return!1}else if(!nt(o)||o.length!==n.length||n.some((i,l)=>i!==o[l]))return!1}return!0}function vr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const gr=(e,t,s)=>e??t??s,du=Be({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const n=st(qn),o=we(()=>e.route||n.value),i=st(pr,0),l=we(()=>{let d=ye(i);const{matched:u}=o.value;let p;for(;(p=u[d])&&!p.components;)d++;return d}),c=we(()=>o.value.matched[l.value]);Ds(pr,we(()=>l.value+1)),Ds(ru,c),Ds(qn,o);const a=W();return Ks(()=>[a.value,c.value,e.name],([d,u,p],[g,v,y])=>{u&&(u.instances[p]=d,v&&v!==u&&d&&d===g&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),d&&u&&(!v||!ls(u,v)||!g)&&(u.enterCallbacks[p]||[]).forEach(w=>w(d))},{flush:"post"}),()=>{const d=o.value,u=e.name,p=c.value,g=p&&p.components[u];if(!g)return mr(s.default,{Component:g,route:d});const v=p.props[u],y=v?v===!0?d.params:typeof v=="function"?v(d):v:null,O=yi(g,ne({},y,t,{onVnodeUnmounted:U=>{U.component.isUnmounted&&(p.instances[u]=null)},ref:a}));return mr(s.default,{Component:O,route:d})||O}}});function mr(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const fu=du;function pu(e){const t=Yc(e.routes,e),s=e.parseQuery||nu,n=e.stringifyQuery||fr,o=e.history,i=hs(),l=hs(),c=hs(),a=vl(Lt);let d=Lt;Qt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Pn.bind(null,S=>""+S),p=Pn.bind(null,$c),g=Pn.bind(null,Rs);function v(S,H){let k,b;return Ai(S)?(k=t.getRecordMatcher(S),b=H):b=S,t.addRoute(b,k)}function y(S){const H=t.getRecordMatcher(S);H&&t.removeRoute(H)}function w(){return t.getRoutes().map(S=>S.record)}function O(S){return!!t.getRecordMatcher(S)}function U(S,H){if(H=ne({},H||a.value),typeof S=="string"){const m=Rn(s,S,H.path),$=t.resolve({path:m.path},H),A=o.createHref(m.fullPath);return ne(m,$,{params:g($.params),hash:Rs(m.hash),redirectedFrom:void 0,href:A})}let k;if(S.path!=null)k=ne({},S,{path:Rn(s,S.path,H.path).path});else{const m=ne({},S.params);for(const $ in m)m[$]==null&&delete m[$];k=ne({},S,{params:p(m)}),H.params=p(H.params)}const b=t.resolve(k,H),L=S.hash||"";b.params=u(g(b.params));const f=Lc(n,ne({},S,{hash:kc(L),path:b.path})),h=o.createHref(f);return ne({fullPath:f,hash:L,query:n===fr?ou(S.query):S.query||{}},b,{redirectedFrom:void 0,href:h})}function x(S){return typeof S=="string"?Rn(s,S,a.value.path):ne({},S)}function _(S,H){if(d!==S)return as(8,{from:H,to:S})}function j(S){return ie(S)}function q(S){return j(ne(x(S),{replace:!0}))}function ue(S){const H=S.matched[S.matched.length-1];if(H&&H.redirect){const{redirect:k}=H;let b=typeof k=="function"?k(S):k;return typeof b=="string"&&(b=b.includes("?")||b.includes("#")?b=x(b):{path:b},b.params={}),ne({query:S.query,hash:S.hash,params:b.path!=null?{}:S.params},b)}}function ie(S,H){const k=d=U(S),b=a.value,L=S.state,f=S.force,h=S.replace===!0,m=ue(k);if(m)return ie(ne(x(m),{state:typeof m=="object"?ne({},L,m.state):L,force:f,replace:h}),H||k);const $=k;$.redirectedFrom=H;let A;return!f&&Pc(n,b,k)&&(A=as(16,{to:$,from:b}),$e(b,b,!0,!1)),(A?Promise.resolve(A):be($,b)).catch(P=>wt(P)?wt(P,2)?P:lt(P):se(P,$,b)).then(P=>{if(P){if(wt(P,2))return ie(ne({replace:h},x(P.to),{state:typeof P.to=="object"?ne({},L,P.to.state):L,force:f}),H||$)}else P=Je($,b,!0,h,L);return Q($,b,P),P})}function F(S,H){const k=_(S,H);return k?Promise.reject(k):Promise.resolve()}function C(S){const H=Ye.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(S):S()}function be(S,H){let k;const[b,L,f]=hu(S,H);k=An(b.reverse(),"beforeRouteLeave",S,H);for(const m of b)m.leaveGuards.forEach($=>{k.push(At($,S,H))});const h=F.bind(null,S,H);return k.push(h),Le(k).then(()=>{k=[];for(const m of i.list())k.push(At(m,S,H));return k.push(h),Le(k)}).then(()=>{k=An(L,"beforeRouteUpdate",S,H);for(const m of L)m.updateGuards.forEach($=>{k.push(At($,S,H))});return k.push(h),Le(k)}).then(()=>{k=[];for(const m of f)if(m.beforeEnter)if(nt(m.beforeEnter))for(const $ of m.beforeEnter)k.push(At($,S,H));else k.push(At(m.beforeEnter,S,H));return k.push(h),Le(k)}).then(()=>(S.matched.forEach(m=>m.enterCallbacks={}),k=An(f,"beforeRouteEnter",S,H,C),k.push(h),Le(k))).then(()=>{k=[];for(const m of l.list())k.push(At(m,S,H));return k.push(h),Le(k)}).catch(m=>wt(m,8)?m:Promise.reject(m))}function Q(S,H,k){c.list().forEach(b=>C(()=>b(S,H,k)))}function Je(S,H,k,b,L){const f=_(S,H);if(f)return f;const h=H===Lt,m=Qt?history.state:{};k&&(b||h?o.replace(S.fullPath,ne({scroll:h&&m&&m.scroll},L)):o.push(S.fullPath,L)),a.value=S,$e(S,H,k,h),lt()}let We;function Ut(){We||(We=o.listen((S,H,k)=>{if(!Gt.listening)return;const b=U(S),L=ue(b);if(L){ie(ne(L,{replace:!0,force:!0}),b).catch(Cs);return}d=b;const f=a.value;Qt&&Uc(nr(f.fullPath,k.delta),mn()),be(b,f).catch(h=>wt(h,12)?h:wt(h,2)?(ie(ne(x(h.to),{force:!0}),b).then(m=>{wt(m,20)&&!k.delta&&k.type===As.pop&&o.go(-1,!1)}).catch(Cs),Promise.reject()):(k.delta&&o.go(-k.delta,!1),se(h,b,f))).then(h=>{h=h||Je(b,f,!1),h&&(k.delta&&!wt(h,8)?o.go(-k.delta,!1):k.type===As.pop&&wt(h,20)&&o.go(-1,!1)),Q(b,f,h)}).catch(Cs)}))}let St=hs(),_e=hs(),te;function se(S,H,k){lt(S);const b=_e.list();return b.length?b.forEach(L=>L(S,H,k)):console.error(S),Promise.reject(S)}function Qe(){return te&&a.value!==Lt?Promise.resolve():new Promise((S,H)=>{St.add([S,H])})}function lt(S){return te||(te=!S,Ut(),St.list().forEach(([H,k])=>S?k(S):H()),St.reset()),S}function $e(S,H,k,b){const{scrollBehavior:L}=e;if(!Qt||!L)return Promise.resolve();const f=!k&&Nc(nr(S.fullPath,0))||(b||!k)&&history.state&&history.state.scroll||null;return lo().then(()=>L(S,H,f)).then(h=>h&&Vc(h)).catch(h=>se(h,S,H))}const Ee=S=>o.go(S);let Et;const Ye=new Set,Gt={currentRoute:a,listening:!0,addRoute:v,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:O,getRoutes:w,resolve:U,options:e,push:j,replace:q,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:i.add,beforeResolve:l.add,afterEach:c.add,onError:_e.add,isReady:Qe,install(S){const H=this;S.component("RouterLink",au),S.component("RouterView",fu),S.config.globalProperties.$router=H,Object.defineProperty(S.config.globalProperties,"$route",{enumerable:!0,get:()=>ye(a)}),Qt&&!Et&&a.value===Lt&&(Et=!0,j(o.location).catch(L=>{}));const k={};for(const L in Lt)Object.defineProperty(k,L,{get:()=>a.value[L],enumerable:!0});S.provide(wn,H),S.provide(go,Br(k)),S.provide(qn,a);const b=S.unmount;Ye.add(S),S.unmount=function(){Ye.delete(S),Ye.size<1&&(d=Lt,We&&We(),We=null,a.value=Lt,Et=!1,te=!1),b()}}};function Le(S){return S.reduce((H,k)=>H.then(()=>C(k)),Promise.resolve())}return Gt}function hu(e,t){const s=[],n=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const c=t.matched[l];c&&(e.matched.find(d=>ls(d,c))?n.push(c):s.push(c));const a=e.matched[l];a&&(t.matched.find(d=>ls(d,a))||o.push(a))}return[s,n,o]}function rt(){return st(wn)}function vu(e){return st(go)}const Mn="auth_token",Tn="user_info";class Zs{static setToken(t){localStorage.setItem(Mn,t)}static getToken(){return localStorage.getItem(Mn)}static removeToken(){localStorage.removeItem(Mn)}static hasToken(){return!!this.getToken()}static getAuthHeader(){const t=this.getToken();return t?`Bearer ${t}`:null}}class Ve{static setUser(t){localStorage.setItem(Tn,JSON.stringify(t))}static getUser(){const t=localStorage.getItem(Tn);if(t)try{return JSON.parse(t)}catch{return this.removeUser(),null}return null}static removeUser(){localStorage.removeItem(Tn)}static isLoggedIn(){return!!this.getUser()&&Zs.hasToken()}static getUserType(){return this.getUser()?.userType||null}static isAdmin(){return this.getUserType()==="ADMIN"}static isUser(){return this.getUserType()==="USER"}}class Te{static login(t,s){Zs.setToken(t),Ve.setUser(s)}static logout(){Zs.removeToken(),Ve.removeUser()}static isAuthenticated(){return Ve.isLoggedIn()}static getCurrentUser(){return Ve.getUser()}static getAuthHeader(){return Zs.getAuthHeader()}}function gu(){return async function(t,s={}){const n=Te.getAuthHeader(),o={"Content-Type":"application/json",...s.headers};n&&(o.Authorization=n);try{const i=await fetch(t,{...s,headers:o});return i.status===401?(Te.logout(),window.location.href="/login",i):i.status===403?(window.location.href="/error/403",i):(i.status>=500&&(window.location.href="/error/500"),i)}catch(i){throw i instanceof TypeError&&i.message.includes("fetch")&&(window.location.href="/error/network"),i}}}const Ii=gu(),Ht=W(null),mu=we(()=>!!Ht.value),Gn=()=>{const e=Ve.getUser();e&&Te.isAuthenticated()?Ht.value=e:Ht.value=null};Gn();function Wt(){const e=a=>{Ht.value=a,Ve.setUser(a)},t=()=>{Gn()},s=()=>{Ht.value=null,Te.logout()},n=()=>{t()},o=()=>{Ht.value=null,Te.logout(),Gn()},i=we(()=>Ve.isAdmin()),l=we(()=>Ve.isUser()),c=we(()=>Ve.getUserType());return{user:we(()=>Ht.value),isLoggedIn:mu,isAdmin:i,isUser:l,userType:c,setUser:e,clearUser:s,initUser:n,logout:o,updateUserState:t}}const us="http://localhost:8080/api/core-features";async function Vs(e,t={}){return(await Ii(e,t)).json()}async function wu(){return(await fetch(`${us}/enabled`)).json()}async function bu(e){return Vs(`${us}/admin/create`,{method:"POST",body:JSON.stringify(e)})}async function yu(e,t){return Vs(`${us}/admin/${e}`,{method:"PUT",body:JSON.stringify(t)})}async function _u(e){return Vs(`${us}/admin/${e}`,{method:"DELETE"})}async function ku(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.title&&t.append("title",e.title),e.status!==void 0&&t.append("status",e.status.toString()),Vs(`${us}/admin/list?${t.toString()}`)}async function Cu(e,t){return Vs(`${us}/admin/${e}/status?status=${t}`,{method:"PUT"})}const xu={class:"home-container"},$u={class:"top-navbar"},Su={class:"nav-content"},Eu={class:"nav-actions"},Lu={class:"welcome-text"},Pu={class:"hero-section"},Ru={class:"hero-content"},Au={class:"hero-actions"},Mu={class:"section-content"},Tu={key:0,class:"loading-container"},Ou={key:1,class:"features-grid"},Iu=["onClick"],Vu=["innerHTML"],Uu={class:"feature-title"},Nu={class:"feature-description"},ju={class:"feature-highlights"},Bu={class:"footer-section"},Hu={class:"footer-content"},Fu={class:"footer-info"},Du={key:0,class:"admin-link"},Ku=Be({__name:"HomeView",setup(e){const t=rt(),{user:s,isLoggedIn:n,initUser:o,logout:i,updateUserState:l}=Wt(),c=W(),a=W([]),d=W(!1),u=async()=>{d.value=!0;try{const y=await wu();y.success&&y.data&&(a.value=y.data)}catch{}finally{d.value=!1}},p=()=>{c.value?.scrollIntoView({behavior:"smooth"})},g=y=>{if(!n.value){t.push("/login");return}},v=()=>{i(),l()};return zt(()=>{o(),u()}),(y,w)=>{const O=ot("router-link");return E(),R("div",xu,[r("nav",$u,[r("div",Su,[w[3]||(w[3]=he('<div class="nav-brand" data-v-2e8e72cd><div class="brand-logo" data-v-2e8e72cd><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-2e8e72cd><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-2e8e72cd></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-2e8e72cd></circle></svg></div><span class="brand-name" data-v-2e8e72cd>膳食营养分析平台</span></div>',1)),r("div",Eu,[ye(n)?(E(),R(ae,{key:0},[r("span",Lu,"欢迎，"+M(ye(s)?.username),1),X(O,{to:"/dashboard",class:"nav-btn dashboard-btn"},{default:pe(()=>w[0]||(w[0]=[G("控制台",-1)])),_:1,__:[0]}),r("button",{onClick:v,class:"nav-btn logout-btn"},"退出登录")],64)):(E(),R(ae,{key:1},[X(O,{to:"/login",class:"nav-btn login-btn"},{default:pe(()=>w[1]||(w[1]=[G("登录",-1)])),_:1,__:[1]}),X(O,{to:"/register",class:"nav-btn register-btn"},{default:pe(()=>w[2]||(w[2]=[G("注册",-1)])),_:1,__:[2]})],64))])])]),w[12]||(w[12]=r("div",{class:"background-decoration"},[r("div",{class:"circle circle-1"}),r("div",{class:"circle circle-2"}),r("div",{class:"circle circle-3"})],-1)),r("section",Pu,[r("div",Ru,[w[5]||(w[5]=he('<h1 class="hero-title" data-v-2e8e72cd>科学饮食，智慧健康</h1><p class="hero-subtitle" data-v-2e8e72cd> 基于AI技术的个性化营养分析平台，为您提供专业的膳食指导和健康管理服务 </p><div class="hero-features" data-v-2e8e72cd><div class="feature-tag" data-v-2e8e72cd>🥗 智能营养分析</div><div class="feature-tag" data-v-2e8e72cd>📊 个性化建议</div><div class="feature-tag" data-v-2e8e72cd>🎯 健康目标管理</div></div>',3)),r("div",Au,[X(O,{to:"/login",class:"cta-btn primary"},{default:pe(()=>w[4]||(w[4]=[G("立即开始",-1)])),_:1,__:[4]}),r("button",{class:"cta-btn secondary",onClick:p},"了解更多")])])]),r("section",{class:"features-section",ref_key:"featuresSection",ref:c},[r("div",Mu,[w[7]||(w[7]=r("div",{class:"section-header"},[r("h2",null,"九大核心功能"),r("p",null,"全方位的营养健康管理服务，从基础记录到智能分析")],-1)),d.value?(E(),R("div",Tu,w[6]||(w[6]=[r("div",{class:"loading-spinner"},null,-1),r("p",null,"正在加载核心功能...",-1)]))):(E(),R("div",Ou,[(E(!0),R(ae,null,et(a.value,U=>(E(),R("div",{key:U.id,class:"feature-card",onClick:x=>g()},[r("div",{class:"feature-icon",innerHTML:U.icon},null,8,Vu),r("h3",Uu,M(U.title),1),r("p",Nu,M(U.description),1),r("div",ju,[(E(!0),R(ae,null,et(U.highlights,x=>(E(),R("span",{key:x,class:"highlight-tag"},M(x),1))),128))])],8,Iu))),128))]))])],512),r("footer",Bu,[r("div",Hu,[w[11]||(w[11]=he('<div class="footer-brand" data-v-2e8e72cd><div class="brand-logo" data-v-2e8e72cd><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-2e8e72cd><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-2e8e72cd></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-2e8e72cd></circle></svg></div><span class="brand-name" data-v-2e8e72cd>膳食营养分析平台</span></div>',1)),r("div",Fu,[w[9]||(w[9]=r("p",null,"© 2025 膳食营养分析平台. 智能营养管理服务",-1)),w[10]||(w[10]=r("p",null,"科学饮食，健康生活",-1)),ye(n)?Y("",!0):(E(),R("div",Du,[X(O,{to:"/admin/login",class:"admin-login-link"},{default:pe(()=>w[8]||(w[8]=[G("管理员登录",-1)])),_:1,__:[8]})]))])])])])}}}),zu=ze(Ku,[["__scopeId","data-v-2e8e72cd"]]);function Wu(e,t,s){Te.isAuthenticated()?Ve.isAdmin()?s():s("/error/403"):s({path:"/admin/login",query:{redirect:e.fullPath}})}function Hs(e,t,s){if(Te.isAuthenticated()){const n=Ve.getUserType();s(n==="ADMIN"?"/admin/dashboard":"/")}else s()}function qu(e){return(t,s,n)=>{if(!Te.isAuthenticated())n({path:"/login",query:{redirect:t.fullPath}});else{const o=Ve.getUserType();o&&e.includes(o)?n():n("/error/403")}}}function wr(e,t,s){Te.isAuthenticated()&&Ve.isAdmin()?s("/admin/dashboard"):s()}const bn="http://localhost:8080/api/user";async function Gu(e){return(await fetch(`${bn}/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json()}async function Zu(e){const s=await(await fetch(`${bn}/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:n,...o}=s.data;n&&Te.login(n,{...o,userType:"USER"})}return s}async function Ju(e){const s=await(await fetch(`${bn}/login-with-code`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:n,...o}=s.data;n&&Te.login(n,{...o,userType:"USER"})}return s}async function Vi(e,t="REGISTER"){return(await fetch(`${bn}/send-verification-code`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:`email=${encodeURIComponent(e)}&type=${encodeURIComponent(t)}`})).json()}const Qu={class:"login-container"},Yu={class:"login-content"},Xu={class:"form-section"},ed={class:"form-container"},td={key:0,class:"success-message"},sd={class:"success-content"},nd={key:1,class:"global-error-message"},od={class:"error-content"},rd={class:"login-mode-switch"},id={class:"form-group"},ld={class:"input-wrapper"},ad={key:0,class:"error-message"},cd={class:"form-group"},ud={class:"input-wrapper"},dd=["type"],fd={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},pd={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},hd={key:0,class:"error-message"},vd={class:"form-group"},gd={class:"input-wrapper"},md={key:0,class:"error-message"},wd={class:"form-group"},bd={class:"verification-input-wrapper"},yd=["disabled"],_d={key:0,class:"loading-spinner"},kd={key:1},Cd={key:2},xd={key:0,class:"error-message"},$d={class:"form-options"},Sd={class:"checkbox-wrapper"},Ed=["disabled"],Ld={key:0,class:"loading-spinner"},Pd={class:"form-footer"},Rd=Be({__name:"LoginView",setup(e){const t=rt(),s=vu(),{updateUserState:n}=Wt(),o=W(!1),i=W(!1),l=W(""),c=W(!1),a=W(!1),d=W(""),u=W("password"),p=W(!1),g=W(0),v=W(null),y=xe({emailOrPhone:"",password:""}),w=xe({email:"",verificationCode:""}),O=xe({}),U=()=>{const F=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;y.emailOrPhone?F.test(y.emailOrPhone)?O.emailOrPhone=void 0:O.emailOrPhone="请输入有效的邮箱地址":O.emailOrPhone="请输入邮箱地址"},x=()=>{y.password?y.password.length<6?O.password="密码长度至少6位":O.password=void 0:O.password="请输入密码"},_=()=>{const F=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;w.email?F.test(w.email)?O.email=void 0:O.email="请输入有效的邮箱地址":O.email="请输入邮箱地址"},j=()=>{w.verificationCode?w.verificationCode.length!==6?O.verificationCode="验证码为6位数字":O.verificationCode=void 0:O.verificationCode="请输入验证码"},q=async()=>{if(_(),!O.email){p.value=!0;try{const F=await Vi(w.email,"LOGIN");F.success?(g.value=60,v.value=setInterval(()=>{g.value--,g.value<=0&&(clearInterval(v.value),v.value=null)},1e3)):d.value=F.message}catch(F){d.value=F.message||"发送验证码失败"}finally{p.value=!1}}},ue=()=>{u.value=u.value==="password"?"code":"password",d.value="",Object.keys(O).forEach(F=>{delete O[F]})},ie=async()=>{if(d.value="",u.value==="password"){if(U(),x(),O.emailOrPhone||O.password)return}else if(_(),j(),O.email||O.verificationCode)return;a.value=!0;try{let F;if(u.value==="password"?F=await Zu({emailOrPhone:y.emailOrPhone,password:y.password}):F=await Ju({email:w.email,verificationCode:w.verificationCode}),F.success&&F.data){const{token:C,...be}=F.data;C&&(Te.login(C,{...be,userType:"USER"}),n()),await new Promise(Je=>setTimeout(Je,200));const Q=s.query.redirect;Q?t.push(Q):t.push("/")}else d.value=F.message}catch(F){d.value=F.message||"登录失败，请检查网络连接后重试"}finally{a.value=!1}};return zt(()=>{const F=s.query.message,C=s.query.type;F&&C==="success"&&(l.value=F,c.value=!0,setTimeout(()=>{c.value=!1},5e3),t.replace({path:"/login"}))}),Os(()=>{v.value&&clearInterval(v.value)}),(F,C)=>{const be=ot("router-link");return E(),R("div",Qu,[C[25]||(C[25]=r("div",{class:"background-decoration"},[r("div",{class:"circle circle-1"}),r("div",{class:"circle circle-2"}),r("div",{class:"circle circle-3"})],-1)),r("div",Yu,[C[24]||(C[24]=he('<div class="brand-section" data-v-63b6f79a><div class="brand-logo" data-v-63b6f79a><div class="logo-icon" data-v-63b6f79a><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-63b6f79a><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-63b6f79a></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-63b6f79a></circle></svg></div><h1 class="brand-title" data-v-63b6f79a>膳食营养分析平台</h1></div><p class="brand-subtitle" data-v-63b6f79a>科学饮食，健康生活</p><div class="feature-list" data-v-63b6f79a><div class="feature-item" data-v-63b6f79a><span class="feature-icon" data-v-63b6f79a>🥗</span><span data-v-63b6f79a>智能营养分析</span></div><div class="feature-item" data-v-63b6f79a><span class="feature-icon" data-v-63b6f79a>📊</span><span data-v-63b6f79a>个性化建议</span></div><div class="feature-item" data-v-63b6f79a><span class="feature-icon" data-v-63b6f79a>🎯</span><span data-v-63b6f79a>健康目标管理</span></div></div></div>',1)),r("div",Xu,[r("div",ed,[C[23]||(C[23]=r("div",{class:"form-header"},[r("h2",null,"欢迎回来"),r("p",null,"登录您的账户，继续您的健康之旅")],-1)),c.value?(E(),R("div",td,[r("div",sd,[C[6]||(C[6]=r("svg",{class:"success-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),r("span",null,M(l.value),1)])])):Y("",!0),d.value?(E(),R("div",nd,[r("div",od,[C[7]||(C[7]=r("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),r("span",null,M(d.value),1)])])):Y("",!0),r("div",rd,[u.value==="code"?(E(),R("button",{key:0,type:"button",class:"mode-button",onClick:ue},C[8]||(C[8]=[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M12 1L15.09 8.26L22 9L15.09 9.74L12 17L8.91 9.74L2 9L8.91 8.26L12 1Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 使用密码登录 ",-1)]))):Y("",!0),u.value==="password"?(E(),R("button",{key:1,type:"button",class:"mode-button",onClick:ue},C[9]||(C[9]=[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 使用验证码登录 ",-1)]))):Y("",!0)]),r("form",{onSubmit:is(ie,["prevent"]),class:"login-form"},[u.value==="password"?(E(),R(ae,{key:0},[r("div",id,[C[11]||(C[11]=r("label",{for:"emailOrPhone",class:"form-label"},"邮箱地址",-1)),r("div",ld,[de(r("input",{id:"emailOrPhone","onUpdate:modelValue":C[0]||(C[0]=Q=>y.emailOrPhone=Q),type:"text",class:ce(["form-input",{error:O.emailOrPhone}]),placeholder:"请输入邮箱地址",onBlur:U},null,34),[[Ce,y.emailOrPhone]]),C[10]||(C[10]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),O.emailOrPhone?(E(),R("span",ad,M(O.emailOrPhone),1)):Y("",!0)]),r("div",cd,[C[14]||(C[14]=r("label",{for:"password",class:"form-label"},"密码",-1)),r("div",ud,[de(r("input",{id:"password","onUpdate:modelValue":C[1]||(C[1]=Q=>y.password=Q),type:o.value?"text":"password",class:ce(["form-input",{error:O.password}]),placeholder:"请输入您的密码",onBlur:x},null,42,dd),[[rs,y.password]]),r("button",{type:"button",class:"password-toggle",onClick:C[2]||(C[2]=Q=>o.value=!o.value)},[o.value?(E(),R("svg",fd,C[12]||(C[12]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(E(),R("svg",pd,C[13]||(C[13]=[r("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),O.password?(E(),R("span",hd,M(O.password),1)):Y("",!0)])],64)):(E(),R(ae,{key:1},[r("div",vd,[C[16]||(C[16]=r("label",{for:"email",class:"form-label"},"邮箱地址",-1)),r("div",gd,[de(r("input",{id:"email","onUpdate:modelValue":C[3]||(C[3]=Q=>w.email=Q),type:"email",class:ce(["form-input",{error:O.email}]),placeholder:"请输入邮箱地址",onBlur:_},null,34),[[Ce,w.email]]),C[15]||(C[15]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),O.email?(E(),R("span",md,M(O.email),1)):Y("",!0)]),r("div",wd,[C[17]||(C[17]=r("label",{for:"verificationCode",class:"form-label"},"验证码",-1)),r("div",bd,[de(r("input",{id:"verificationCode","onUpdate:modelValue":C[4]||(C[4]=Q=>w.verificationCode=Q),type:"text",class:ce(["form-input verification-input",{error:O.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:j},null,34),[[Ce,w.verificationCode]]),r("button",{type:"button",class:"send-code-button",disabled:p.value||g.value>0||!w.email,onClick:q},[p.value?(E(),R("span",_d)):g.value>0?(E(),R("span",kd,M(g.value)+"s",1)):(E(),R("span",Cd,"发送验证码"))],8,yd)]),O.verificationCode?(E(),R("span",xd,M(O.verificationCode),1)):Y("",!0)])],64)),r("div",$d,[r("label",Sd,[de(r("input",{type:"checkbox","onUpdate:modelValue":C[5]||(C[5]=Q=>i.value=Q)},null,512),[[ho,i.value]]),C[18]||(C[18]=r("span",{class:"checkmark"},null,-1)),C[19]||(C[19]=r("span",{class:"checkbox-label"},"记住我",-1))]),C[20]||(C[20]=r("a",{href:"#",class:"forgot-password"},"忘记密码？",-1))]),r("button",{type:"submit",class:"login-button",disabled:a.value},[a.value?(E(),R("span",Ld)):Y("",!0),r("span",null,M(a.value?"登录中...":"登录"),1)],8,Ed)],32),r("div",Pd,[r("p",null,[C[22]||(C[22]=G("还没有账户？ ",-1)),X(be,{to:"/register",class:"register-link"},{default:pe(()=>C[21]||(C[21]=[G("立即注册",-1)])),_:1,__:[21]})])])])])])])}}}),Ad=ze(Rd,[["__scopeId","data-v-63b6f79a"]]),Md={class:"password-strength-meter"},Td={class:"strength-bar"},Od={class:"strength-text"},Id={key:0,class:"suggestions"},Vd=Be({__name:"PasswordStrengthMeter",props:{password:{}},setup(e){const t=e,s=we(()=>{const c=t.password;if(!c)return{score:0,text:"",suggestions:[]};let a=0;const d=[];c.length>=8?a+=1:d.push("密码长度至少8位"),c.length>=12?a+=1:c.length>=8&&d.push("建议密码长度12位以上"),/[a-z]/.test(c)?a+=1:d.push("包含小写字母"),/[A-Z]/.test(c)?a+=1:d.push("包含大写字母"),/[0-9]/.test(c)?a+=1:d.push("包含数字"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(c)?a+=1:d.push("包含特殊字符"),["123456","password","123456789","12345678","12345","1234567","admin","123123","qwerty","abc123"].includes(c.toLowerCase())&&(a=0,d.push("避免使用常见密码")),/(.)\1{2,}/.test(c)&&(a-=1,d.push("避免连续重复字符")),a=Math.max(0,Math.min(6,a));let p="";return a===0?p="很弱":a<=2?p="弱":a<=4?p="中等":p="强",{score:a,text:p,suggestions:d}}),n=we(()=>s.value.score/6*100),o=we(()=>s.value.text),i=we(()=>{const c=s.value.score;return c===0?"very-weak":c<=2?"weak":c<=4?"medium":"strong"}),l=we(()=>s.value.suggestions);return(c,a)=>(E(),R("div",Md,[r("div",Td,[r("div",{class:ce(["strength-fill",i.value]),style:un({width:n.value+"%"})},null,6)]),r("div",Od,[r("span",{class:ce(i.value)},M(o.value),3)]),l.value.length>0?(E(),R("div",Id,[a[0]||(a[0]=r("p",{class:"suggestions-title"},"密码建议：",-1)),r("ul",null,[(E(!0),R(ae,null,et(l.value,d=>(E(),R("li",{key:d},M(d),1))),128))])])):Y("",!0)]))}}),Ud=ze(Vd,[["__scopeId","data-v-3b5f4836"]]),Nd={class:"register-container"},jd={class:"register-content"},Bd={class:"form-section"},Hd={class:"form-container"},Fd={key:0,class:"success-message"},Dd={class:"success-content"},Kd={key:1,class:"global-error-message"},zd={class:"error-content"},Wd={class:"form-group"},qd={class:"input-wrapper"},Gd={key:0,class:"error-message"},Zd={class:"form-group"},Jd={class:"input-wrapper"},Qd={key:0,class:"error-message"},Yd={class:"form-group"},Xd={class:"verification-input-wrapper"},ef=["disabled"],tf={key:0,class:"error-message"},sf={class:"form-group"},nf={class:"input-wrapper"},of=["type"],rf={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},lf={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},af={key:1,class:"error-message"},cf={class:"form-group"},uf={class:"input-wrapper"},df=["type"],ff={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},pf={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},hf={key:0,class:"error-message"},vf={class:"form-group"},gf={class:"checkbox-wrapper"},mf=["disabled"],wf={key:0,class:"loading-spinner"},bf={class:"form-footer"},yf=Be({__name:"RegisterView",setup(e){const t=rt(),s=W(!1),n=W(!1),o=W(!1),i=W(!1),l=W(""),c=W(!1),a=W(""),d=xe({username:"",email:"",password:"",confirmPassword:"",verificationCode:""}),u=xe({}),p=W(!1),g=W(0),v=we(()=>d.email&&!u.email&&g.value===0),y=we(()=>{const F=d.password;if(!F)return{score:0,text:"",class:"",width:"0%"};let C=0;return F.length>=8&&(C+=1),F.length>=12&&(C+=1),/[a-z]/.test(F)&&(C+=1),/[A-Z]/.test(F)&&(C+=1),/[0-9]/.test(F)&&(C+=1),/[^A-Za-z0-9]/.test(F)&&(C+=1),C<=2?{score:C,text:"弱",class:"weak",width:"33%"}:C<=4?{score:C,text:"中等",class:"medium",width:"66%"}:{score:C,text:"强",class:"strong",width:"100%"}}),w=()=>{d.username?d.username.length<3?u.username="用户名至少3个字符":d.username.length>20?u.username="用户名不能超过20个字符":/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(d.username)?u.username=void 0:u.username="用户名只能包含字母、数字、下划线和中文":u.username="请输入用户名"},O=()=>{const F=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;d.email?F.test(d.email)?u.email=void 0:u.email="请输入有效的邮箱地址":u.email="请输入邮箱地址"},U=()=>{d.password?d.password.length<8?u.password="密码长度至少8位":y.value.score<2?u.password="密码强度太弱，请包含字母、数字或特殊字符":u.password=void 0:u.password="请输入密码"},x=()=>{d.confirmPassword?d.password!==d.confirmPassword?u.confirmPassword="两次输入的密码不一致":u.confirmPassword=void 0:u.confirmPassword="请确认密码"},_=()=>{d.confirmPassword&&d.password!==d.confirmPassword?u.confirmPassword="两次输入的密码不一致":u.confirmPassword=void 0},j=()=>{d.verificationCode?d.verificationCode.length!==6?u.verificationCode="验证码必须是6位数字":/^\d{6}$/.test(d.verificationCode)?u.verificationCode=void 0:u.verificationCode="验证码只能包含数字":u.verificationCode="请输入验证码"},q=()=>{d.verificationCode=d.verificationCode.replace(/\D/g,""),d.verificationCode.length===6&&j()},ue=async()=>{if(!(!v.value||p.value)){p.value=!0;try{const F=await Vi(d.email,"REGISTER");if(F.success){g.value=60;const C=setInterval(()=>{g.value--,g.value<=0&&clearInterval(C)},1e3);a.value=""}else a.value=F.message||"发送验证码失败"}catch{a.value="发送验证码失败，请检查网络连接"}finally{p.value=!1}}},ie=async()=>{if(a.value="",w(),O(),U(),x(),j(),!(u.username||u.email||u.password||u.confirmPassword||u.verificationCode)){if(!o.value){a.value="请同意用户协议和隐私政策";return}i.value=!0;try{await new Promise(C=>setTimeout(C,1500));const F=await Gu({username:d.username,password:d.password,email:d.email,verificationCode:d.verificationCode});F.success?(l.value="注册成功！即将跳转到登录页面...",c.value=!0,setTimeout(()=>{t.push({path:"/login",query:{message:"注册成功！请使用您的邮箱或手机号登录",type:"success"}})},2e3)):F.message.includes("用户名")?u.username=F.message:F.message.includes("邮箱")?u.email=F.message:F.message.includes("验证码")?u.verificationCode=F.message:a.value=F.message}catch{a.value="注册失败，请检查网络连接后重试"}finally{i.value=!1}}};return(F,C)=>{const be=ot("router-link");return E(),R("div",Nd,[C[27]||(C[27]=he('<div class="background-decoration" data-v-7a143c20><div class="circle circle-1" data-v-7a143c20></div><div class="circle circle-2" data-v-7a143c20></div><div class="circle circle-3" data-v-7a143c20></div><div class="wave-pattern" data-v-7a143c20></div></div>',1)),r("div",jd,[r("div",Bd,[r("div",Hd,[C[25]||(C[25]=r("div",{class:"form-header"},[r("h2",null,"开启健康之旅"),r("p",null,"创建您的账户，开始科学的营养管理")],-1)),c.value?(E(),R("div",Fd,[r("div",Dd,[C[8]||(C[8]=r("svg",{class:"success-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),r("span",null,M(l.value),1)])])):Y("",!0),a.value?(E(),R("div",Kd,[r("div",zd,[C[9]||(C[9]=r("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),r("span",null,M(a.value),1)])])):Y("",!0),r("form",{onSubmit:is(ie,["prevent"]),class:"register-form"},[r("div",Wd,[C[11]||(C[11]=r("label",{for:"username",class:"form-label"},"用户名",-1)),r("div",qd,[de(r("input",{id:"username","onUpdate:modelValue":C[0]||(C[0]=Q=>d.username=Q),type:"text",class:ce(["form-input",{error:u.username}]),placeholder:"请输入用户名",onBlur:w},null,34),[[Ce,d.username]]),C[10]||(C[10]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("circle",{cx:"12",cy:"7",r:"4",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),u.username?(E(),R("span",Gd,M(u.username),1)):Y("",!0)]),r("div",Zd,[C[13]||(C[13]=r("label",{for:"email",class:"form-label"},"邮箱地址",-1)),r("div",Jd,[de(r("input",{id:"email","onUpdate:modelValue":C[1]||(C[1]=Q=>d.email=Q),type:"email",class:ce(["form-input",{error:u.email}]),placeholder:"请输入您的邮箱",onBlur:O},null,34),[[Ce,d.email]]),C[12]||(C[12]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),u.email?(E(),R("span",Qd,M(u.email),1)):Y("",!0)]),r("div",Yd,[C[14]||(C[14]=r("label",{for:"verificationCode",class:"form-label"},"邮箱验证码",-1)),r("div",Xd,[de(r("input",{id:"verificationCode","onUpdate:modelValue":C[2]||(C[2]=Q=>d.verificationCode=Q),type:"text",class:ce(["form-input verification-input",{error:u.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:j,onInput:q},null,34),[[Ce,d.verificationCode]]),r("button",{type:"button",class:ce(["send-code-btn",{disabled:!v.value||p.value}]),disabled:!v.value||p.value,onClick:ue},M(p.value?"发送中...":g.value>0?`${g.value}s后重发`:"发送验证码"),11,ef)]),u.verificationCode?(E(),R("span",tf,M(u.verificationCode),1)):Y("",!0)]),r("div",sf,[C[17]||(C[17]=r("label",{for:"password",class:"form-label"},"密码",-1)),r("div",nf,[de(r("input",{id:"password","onUpdate:modelValue":C[3]||(C[3]=Q=>d.password=Q),type:s.value?"text":"password",class:ce(["form-input",{error:u.password}]),placeholder:"请设置密码（至少8位）",onBlur:U,onInput:_},null,42,of),[[rs,d.password]]),r("button",{type:"button",class:"password-toggle",onClick:C[4]||(C[4]=Q=>s.value=!s.value)},[s.value?(E(),R("svg",rf,C[15]||(C[15]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(E(),R("svg",lf,C[16]||(C[16]=[r("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),d.password?(E(),Kt(Ud,{key:0,password:d.password},null,8,["password"])):Y("",!0),u.password?(E(),R("span",af,M(u.password),1)):Y("",!0)]),r("div",cf,[C[20]||(C[20]=r("label",{for:"confirmPassword",class:"form-label"},"确认密码",-1)),r("div",uf,[de(r("input",{id:"confirmPassword","onUpdate:modelValue":C[5]||(C[5]=Q=>d.confirmPassword=Q),type:n.value?"text":"password",class:ce(["form-input",{error:u.confirmPassword}]),placeholder:"请再次输入密码",onBlur:x},null,42,df),[[rs,d.confirmPassword]]),r("button",{type:"button",class:"password-toggle",onClick:C[6]||(C[6]=Q=>n.value=!n.value)},[n.value?(E(),R("svg",ff,C[18]||(C[18]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(E(),R("svg",pf,C[19]||(C[19]=[r("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),u.confirmPassword?(E(),R("span",hf,M(u.confirmPassword),1)):Y("",!0)]),r("div",vf,[r("label",gf,[de(r("input",{type:"checkbox","onUpdate:modelValue":C[7]||(C[7]=Q=>o.value=Q)},null,512),[[ho,o.value]]),C[21]||(C[21]=r("span",{class:"checkmark"},null,-1)),C[22]||(C[22]=r("span",{class:"checkbox-label"},[G(" 我已阅读并同意 "),r("a",{href:"#",class:"terms-link"},"用户协议"),G(" 和 "),r("a",{href:"#",class:"terms-link"},"隐私政策")],-1))])]),r("button",{type:"submit",class:"register-button",disabled:i.value||!o.value},[i.value?(E(),R("span",wf)):Y("",!0),r("span",null,M(i.value?"注册中...":"创建账户"),1)],8,mf)],32),r("div",bf,[r("p",null,[C[24]||(C[24]=G("已有账户？ ",-1)),X(be,{to:"/login",class:"login-link"},{default:pe(()=>C[23]||(C[23]=[G("立即登录",-1)])),_:1,__:[23]})])])])]),C[26]||(C[26]=he('<div class="brand-section" data-v-7a143c20><div class="brand-content" data-v-7a143c20><div class="brand-logo" data-v-7a143c20><div class="logo-icon" data-v-7a143c20><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-7a143c20><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-7a143c20></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-7a143c20></circle></svg></div><h1 class="brand-title" data-v-7a143c20>膳食营养分析平台</h1></div><div class="benefits-list" data-v-7a143c20><div class="benefit-item" data-v-7a143c20><div class="benefit-icon" data-v-7a143c20>🎯</div><div class="benefit-content" data-v-7a143c20><h3 data-v-7a143c20>个性化目标</h3><p data-v-7a143c20>根据您的身体状况和健康目标，制定专属营养计划</p></div></div><div class="benefit-item" data-v-7a143c20><div class="benefit-icon" data-v-7a143c20>📊</div><div class="benefit-content" data-v-7a143c20><h3 data-v-7a143c20>智能分析</h3><p data-v-7a143c20>AI驱动的营养成分分析，让每一餐都更科学</p></div></div><div class="benefit-item" data-v-7a143c20><div class="benefit-icon" data-v-7a143c20>🏆</div><div class="benefit-content" data-v-7a143c20><h3 data-v-7a143c20>持续改进</h3><p data-v-7a143c20>跟踪您的进步，不断优化饮食建议</p></div></div></div></div></div>',1))])])}}}),_f=ze(yf,[["__scopeId","data-v-7a143c20"]]),kf={class:"dashboard-container"},Cf={class:"top-navbar"},xf={class:"nav-content"},$f={class:"nav-actions"},Sf={class:"welcome-text"},Ef={class:"dashboard-main"},Lf={class:"welcome-section"},Pf={class:"welcome-content"},Rf={class:"welcome-title"},Af={class:"features-section"},Mf={class:"features-grid"},Tf=Be({__name:"DashboardView",setup(e){const t=rt(),{user:s,logout:n,updateUserState:o}=Wt(),i=()=>{n(),o(),t.push("/")},l=c=>{};return zt(()=>{}),(c,a)=>{const d=ot("router-link");return E(),R("div",kf,[r("nav",Cf,[r("div",xf,[a[5]||(a[5]=he('<div class="nav-brand" data-v-273412f9><div class="brand-logo" data-v-273412f9><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-273412f9><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-273412f9></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-273412f9></circle></svg></div><span class="brand-name" data-v-273412f9>膳食营养分析平台</span></div>',1)),r("div",$f,[X(d,{to:"/",class:"nav-btn home-btn"},{default:pe(()=>a[4]||(a[4]=[G("首页",-1)])),_:1,__:[4]}),r("span",Sf,"欢迎，"+M(ye(s)?.username),1),r("button",{onClick:i,class:"nav-btn logout-btn"},"退出登录")])])]),r("main",Ef,[r("section",Lf,[r("div",Pf,[r("h1",Rf,"欢迎回来，"+M(ye(s)?.username)+"！",1),a[6]||(a[6]=r("p",{class:"welcome-subtitle"},"开始您的健康饮食管理之旅",-1))])]),r("section",Af,[a[11]||(a[11]=r("h2",{class:"section-title"},"功能中心",-1)),r("div",Mf,[r("div",{class:"feature-card",onClick:a[0]||(a[0]=u=>l())},a[7]||(a[7]=[he('<div class="feature-icon" data-v-273412f9>🥗</div><h3 class="feature-title" data-v-273412f9>营养分析</h3><p class="feature-description" data-v-273412f9>分析食物营养成分，制定健康饮食计划</p><div class="feature-action" data-v-273412f9><span class="action-text" data-v-273412f9>立即使用</span><svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor" data-v-273412f9><path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" data-v-273412f9></path></svg></div>',4)])),r("div",{class:"feature-card",onClick:a[1]||(a[1]=u=>l())},a[8]||(a[8]=[he('<div class="feature-icon" data-v-273412f9>📝</div><h3 class="feature-title" data-v-273412f9>饮食记录</h3><p class="feature-description" data-v-273412f9>记录每日饮食，追踪营养摄入情况</p><div class="feature-action" data-v-273412f9><span class="action-text" data-v-273412f9>立即使用</span><svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor" data-v-273412f9><path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" data-v-273412f9></path></svg></div>',4)])),r("div",{class:"feature-card",onClick:a[2]||(a[2]=u=>l())},a[9]||(a[9]=[he('<div class="feature-icon" data-v-273412f9>🎯</div><h3 class="feature-title" data-v-273412f9>健康目标</h3><p class="feature-description" data-v-273412f9>设置个人健康目标，监控达成进度</p><div class="feature-action" data-v-273412f9><span class="action-text" data-v-273412f9>立即使用</span><svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor" data-v-273412f9><path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" data-v-273412f9></path></svg></div>',4)])),r("div",{class:"feature-card",onClick:a[3]||(a[3]=u=>l())},a[10]||(a[10]=[he('<div class="feature-icon" data-v-273412f9>📊</div><h3 class="feature-title" data-v-273412f9>健康报告</h3><p class="feature-description" data-v-273412f9>生成详细的营养分析和健康评估报告</p><div class="feature-action" data-v-273412f9><span class="action-text" data-v-273412f9>立即使用</span><svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor" data-v-273412f9><path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" data-v-273412f9></path></svg></div>',4)]))])])])])}}}),Of=ze(Tf,[["__scopeId","data-v-273412f9"]]),it="http://localhost:8080/api/admin";async function qt(e,t={}){return(await Ii(e,t)).json()}async function If(e){const s=await(await fetch(`${it}/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:n,...o}=s.data;n&&Te.login(n,{...o,userType:"ADMIN"})}return s}async function Vf(e){return(await fetch(`${it}/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json()}async function Uf(e){return qt(`${it}/${e}`,{method:"DELETE"})}async function Nf(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.username&&t.append("username",e.username),e.realName&&t.append("realName",e.realName),e.status!==void 0&&t.append("status",e.status.toString()),qt(`${it}/list?${t.toString()}`)}async function jf(e,t){return qt(`${it}/${e}/status?status=${t}`,{method:"PUT"})}async function Bf(e){return(await fetch(`${it}/check-username?username=${encodeURIComponent(e)}`)).json()}async function Hf(e){return(await fetch(`${it}/check-email?email=${encodeURIComponent(e)}`)).json()}async function Ff(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.username&&t.append("username",e.username),e.email&&t.append("email",e.email),e.phone&&t.append("phone",e.phone),e.status!==void 0&&t.append("status",e.status.toString()),qt(`${it}/users?${t.toString()}`)}async function Df(e,t){return qt(`${it}/users/${e}/status?status=${t}`,{method:"PUT"})}async function Kf(e){return qt(`${it}/users/${e}`,{method:"DELETE"})}async function zf(){return qt(`${it}/users/stats`)}const Wf={class:"login-container"},qf={class:"login-content"},Gf={class:"form-section"},Zf={class:"form-container"},Jf={key:0,class:"global-error-message"},Qf={class:"error-content"},Yf={class:"form-group"},Xf={class:"input-wrapper"},e1={key:0,class:"error-message"},t1={class:"form-group"},s1={class:"input-wrapper"},n1=["type"],o1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i1={key:0,class:"error-message"},l1=["disabled"],a1={key:0,class:"loading-spinner"},c1={class:"form-footer"},u1={class:"footer-links"},d1=Be({__name:"AdminLoginView",setup(e){const t=rt(),s=xe({username:"",password:""}),n=xe({username:"",password:""}),o=W(!1),i=W(!1),l=W(""),c=()=>{i.value=!i.value},a=()=>{s.username.trim()?n.username="":n.username="请输入管理员用户名"},d=()=>{s.password.trim()?n.password="":n.password="请输入密码"},u=()=>(a(),d(),l.value="",!n.username&&!n.password),p=async()=>{if(u()){o.value=!0;try{const g=await If({username:s.username,password:s.password});if(g.success&&g.data){const{token:v,...y}=g.data;v&&Te.login(v,{...y,userType:"ADMIN"}),t.push("/admin/dashboard")}else l.value=g.message}catch(g){l.value=g.message||"登录失败，请检查网络连接后重试"}finally{o.value=!1}}};return(g,v)=>{const y=ot("router-link");return E(),R("div",Wf,[v[13]||(v[13]=r("div",{class:"background-decoration"},[r("div",{class:"circle circle-1"}),r("div",{class:"circle circle-2"}),r("div",{class:"circle circle-3"})],-1)),r("div",qf,[v[12]||(v[12]=he('<div class="brand-section" data-v-ab9fd176><div class="brand-logo" data-v-ab9fd176><div class="logo-container" data-v-ab9fd176><div class="logo-circle" data-v-ab9fd176><span class="logo-text" data-v-ab9fd176>🛡️</span></div><div class="logo-ripple" data-v-ab9fd176></div></div><h1 class="brand-title" data-v-ab9fd176><span class="title-char" style="animation-delay:0.1s;" data-v-ab9fd176>膳</span><span class="title-char" style="animation-delay:0.2s;" data-v-ab9fd176>食</span><span class="title-char" style="animation-delay:0.3s;" data-v-ab9fd176>营</span><span class="title-char" style="animation-delay:0.4s;" data-v-ab9fd176>养</span><span class="title-char" style="animation-delay:0.5s;" data-v-ab9fd176>分</span><span class="title-char" style="animation-delay:0.6s;" data-v-ab9fd176>析</span><span class="title-char" style="animation-delay:0.7s;" data-v-ab9fd176>平</span><span class="title-char" style="animation-delay:0.8s;" data-v-ab9fd176>台</span></h1><p class="brand-subtitle" data-v-ab9fd176>管理后台 - 系统管理中心</p></div><div class="feature-highlights" data-v-ab9fd176><div class="feature-item" data-v-ab9fd176><div class="feature-icon" data-v-ab9fd176>👥</div><div class="feature-content" data-v-ab9fd176><h3 data-v-ab9fd176>用户管理</h3><p data-v-ab9fd176>全面的用户账户管理</p></div></div><div class="feature-item" data-v-ab9fd176><div class="feature-icon" data-v-ab9fd176>📊</div><div class="feature-content" data-v-ab9fd176><h3 data-v-ab9fd176>数据统计</h3><p data-v-ab9fd176>详细的数据分析报告</p></div></div><div class="feature-item" data-v-ab9fd176><div class="feature-icon" data-v-ab9fd176>⚙️</div><div class="feature-content" data-v-ab9fd176><h3 data-v-ab9fd176>系统配置</h3><p data-v-ab9fd176>灵活的系统参数设置</p></div></div></div></div>',1)),r("div",Gf,[r("div",Zf,[v[11]||(v[11]=r("div",{class:"form-header"},[r("h2",{class:"form-title"},"管理员登录"),r("div",{class:"title-underline"}),r("p",{class:"form-subtitle"},"请使用管理员账户登录系统")],-1)),l.value?(E(),R("div",Jf,[r("div",Qf,[v[2]||(v[2]=r("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),r("span",null,M(l.value),1)])])):Y("",!0),r("form",{onSubmit:is(p,["prevent"]),class:"login-form"},[r("div",Yf,[v[4]||(v[4]=r("label",{for:"username",class:"form-label"},"管理员用户名",-1)),r("div",Xf,[de(r("input",{id:"username","onUpdate:modelValue":v[0]||(v[0]=w=>s.username=w),type:"text",class:ce(["form-input",{error:n.username}]),placeholder:"请输入管理员用户名",onBlur:a},null,34),[[Ce,s.username]]),v[3]||(v[3]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),n.username?(E(),R("span",e1,M(n.username),1)):Y("",!0)]),r("div",t1,[v[8]||(v[8]=r("label",{for:"password",class:"form-label"},"密码",-1)),r("div",s1,[de(r("input",{id:"password","onUpdate:modelValue":v[1]||(v[1]=w=>s.password=w),type:i.value?"text":"password",class:ce(["form-input",{error:n.password}]),placeholder:"请输入密码",onBlur:d},null,42,n1),[[rs,s.password]]),v[7]||(v[7]=he('<span class="input-icon" data-v-ab9fd176><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-ab9fd176><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" data-v-ab9fd176></rect><circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" data-v-ab9fd176></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2" data-v-ab9fd176></path></svg></span>',1)),r("button",{type:"button",class:"password-toggle",onClick:c},[i.value?(E(),R("svg",o1,v[5]||(v[5]=[r("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",stroke:"currentColor","stroke-width":"2"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},null,-1)]))):(E(),R("svg",r1,v[6]||(v[6]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",stroke:"currentColor","stroke-width":"2"},null,-1),r("line",{x1:"1",y1:"1",x2:"23",y2:"23",stroke:"currentColor","stroke-width":"2"},null,-1)])))])]),n.password?(E(),R("span",i1,M(n.password),1)):Y("",!0)]),r("button",{type:"submit",class:"login-button",disabled:o.value},[o.value?(E(),R("span",a1)):Y("",!0),r("span",null,M(o.value?"登录中...":"登录管理后台"),1)],8,l1)],32),r("div",c1,[r("div",u1,[X(y,{to:"/admin/register",class:"register-link"},{default:pe(()=>v[9]||(v[9]=[G("还没有管理员账户？立即注册",-1)])),_:1,__:[9]}),X(y,{to:"/",class:"back-link"},{default:pe(()=>v[10]||(v[10]=[G("← 返回首页",-1)])),_:1,__:[10]})])])])])])])}}}),f1=ze(d1,[["__scopeId","data-v-ab9fd176"]]),p1={class:"register-container"},h1={class:"register-content"},v1={class:"form-section"},g1={class:"form-container"},m1={key:0,class:"global-error-message"},w1={class:"error-content"},b1={key:1,class:"success-message"},y1={class:"success-content"},_1={class:"form-group"},k1={class:"input-wrapper"},C1={key:0,class:"error-message"},x1={class:"form-group"},$1={class:"input-wrapper"},S1={key:0,class:"error-message"},E1={class:"form-group"},L1={class:"input-wrapper"},P1={key:0,class:"error-message"},R1={class:"form-group"},A1={class:"input-wrapper"},M1=["type"],T1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},O1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},I1={key:0,class:"error-message"},V1={class:"form-group"},U1={class:"input-wrapper"},N1=["type"],j1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},B1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},H1={key:0,class:"error-message"},F1=["disabled"],D1={key:0,class:"loading-spinner"},K1={class:"form-footer"},z1={class:"footer-links"},W1=Be({__name:"AdminRegisterView",setup(e){const t=rt(),s=xe({username:"",email:"",realName:"",password:"",confirmPassword:""}),n=xe({username:"",email:"",realName:"",password:"",confirmPassword:""}),o=W(!1),i=W(!1),l=W(!1),c=W(""),a=W(""),d=()=>{i.value=!i.value},u=()=>{l.value=!l.value},p=async()=>{if(!s.username.trim())return n.username="请输入管理员用户名",!1;if(s.username.length<3)return n.username="用户名至少需要3个字符",!1;if(s.username.length>20)return n.username="用户名不能超过20个字符",!1;if(!/^[a-zA-Z0-9_]+$/.test(s.username))return n.username="用户名只能包含字母、数字和下划线",!1;try{const x=await Bf(s.username);if(x.success&&!x.data)return n.username="该用户名已被使用",!1}catch{}return n.username="",!0},g=async()=>{if(!s.email.trim())return n.email="请输入邮箱地址",!1;if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s.email))return n.email="请输入有效的邮箱地址",!1;try{const _=await Hf(s.email);if(_.success&&!_.data)return n.email="该邮箱已被使用",!1}catch{}return n.email="",!0},v=()=>s.realName.trim()?s.realName.length<2?(n.realName="真实姓名至少需要2个字符",!1):s.realName.length>10?(n.realName="真实姓名不能超过10个字符",!1):(n.realName="",!0):(n.realName="请输入真实姓名",!1),y=()=>s.password.trim()?s.password.length<6?(n.password="密码至少需要6个字符",!1):s.password.length>20?(n.password="密码不能超过20个字符",!1):(n.password="",!0):(n.password="请输入密码",!1),w=()=>s.confirmPassword.trim()?s.confirmPassword!==s.password?(n.confirmPassword="两次输入的密码不一致",!1):(n.confirmPassword="",!0):(n.confirmPassword="请确认密码",!1),O=async()=>{const x=await p(),_=await g(),j=v(),q=y(),ue=w();return c.value="",a.value="",x&&_&&j&&q&&ue},U=async()=>{if(await O()){o.value=!0;try{const x=await Vf({username:s.username,email:s.email,realName:s.realName,password:s.password});x.success?(a.value="管理员账户创建成功！即将跳转到登录页面...",Object.keys(s).forEach(_=>{s[_]=""}),setTimeout(()=>{t.push("/admin/login")},3e3)):c.value=x.message}catch(x){c.value=x.message||"注册失败，请检查网络连接后重试"}finally{o.value=!1}}};return(x,_)=>{const j=ot("router-link");return E(),R("div",p1,[_[25]||(_[25]=r("div",{class:"background-decoration"},[r("div",{class:"circle circle-1"}),r("div",{class:"circle circle-2"}),r("div",{class:"circle circle-3"})],-1)),r("div",h1,[_[24]||(_[24]=he('<div class="brand-section" data-v-2b12c018><div class="brand-logo" data-v-2b12c018><div class="logo-container" data-v-2b12c018><div class="logo-circle" data-v-2b12c018><span class="logo-text" data-v-2b12c018>🛡️</span></div><div class="logo-ripple" data-v-2b12c018></div></div><h1 class="brand-title" data-v-2b12c018><span class="title-char" style="animation-delay:0.1s;" data-v-2b12c018>膳</span><span class="title-char" style="animation-delay:0.2s;" data-v-2b12c018>食</span><span class="title-char" style="animation-delay:0.3s;" data-v-2b12c018>营</span><span class="title-char" style="animation-delay:0.4s;" data-v-2b12c018>养</span><span class="title-char" style="animation-delay:0.5s;" data-v-2b12c018>分</span><span class="title-char" style="animation-delay:0.6s;" data-v-2b12c018>析</span><span class="title-char" style="animation-delay:0.7s;" data-v-2b12c018>平</span><span class="title-char" style="animation-delay:0.8s;" data-v-2b12c018>台</span></h1><p class="brand-subtitle" data-v-2b12c018>管理后台 - 管理员注册</p></div><div class="feature-highlights" data-v-2b12c018><div class="feature-item" data-v-2b12c018><div class="feature-icon" data-v-2b12c018>🔐</div><div class="feature-content" data-v-2b12c018><h3 data-v-2b12c018>安全管理</h3><p data-v-2b12c018>严格的权限控制体系</p></div></div><div class="feature-item" data-v-2b12c018><div class="feature-icon" data-v-2b12c018>⚡</div><div class="feature-content" data-v-2b12c018><h3 data-v-2b12c018>高效运营</h3><p data-v-2b12c018>强大的管理工具集</p></div></div><div class="feature-item" data-v-2b12c018><div class="feature-icon" data-v-2b12c018>📈</div><div class="feature-content" data-v-2b12c018><h3 data-v-2b12c018>数据洞察</h3><p data-v-2b12c018>全面的分析报告</p></div></div></div></div>',1)),r("div",v1,[r("div",g1,[_[23]||(_[23]=r("div",{class:"form-header"},[r("h2",{class:"form-title"},"管理员注册"),r("div",{class:"title-underline"}),r("p",{class:"form-subtitle"},"创建新的管理员账户")],-1)),c.value?(E(),R("div",m1,[r("div",w1,[_[5]||(_[5]=r("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),r("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),r("span",null,M(c.value),1)])])):Y("",!0),a.value?(E(),R("div",b1,[r("div",y1,[_[6]||(_[6]=r("svg",{class:"success-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M9 12l2 2 4-4",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"})],-1)),r("span",null,M(a.value),1)])])):Y("",!0),r("form",{onSubmit:is(U,["prevent"]),class:"register-form"},[r("div",_1,[_[8]||(_[8]=r("label",{for:"username",class:"form-label"},"管理员用户名",-1)),r("div",k1,[de(r("input",{id:"username","onUpdate:modelValue":_[0]||(_[0]=q=>s.username=q),type:"text",class:ce(["form-input",{error:n.username}]),placeholder:"请输入管理员用户名",onBlur:p},null,34),[[Ce,s.username]]),_[7]||(_[7]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),n.username?(E(),R("span",C1,M(n.username),1)):Y("",!0)]),r("div",x1,[_[10]||(_[10]=r("label",{for:"email",class:"form-label"},"邮箱地址",-1)),r("div",$1,[de(r("input",{id:"email","onUpdate:modelValue":_[1]||(_[1]=q=>s.email=q),type:"email",class:ce(["form-input",{error:n.email}]),placeholder:"请输入邮箱地址",onBlur:g},null,34),[[Ce,s.email]]),_[9]||(_[9]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z",stroke:"currentColor","stroke-width":"2"}),r("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2"})])],-1))]),n.email?(E(),R("span",S1,M(n.email),1)):Y("",!0)]),r("div",E1,[_[12]||(_[12]=r("label",{for:"realName",class:"form-label"},"真实姓名",-1)),r("div",L1,[de(r("input",{id:"realName","onUpdate:modelValue":_[2]||(_[2]=q=>s.realName=q),type:"text",class:ce(["form-input",{error:n.realName}]),placeholder:"请输入真实姓名",onBlur:v},null,34),[[Ce,s.realName]]),_[11]||(_[11]=r("span",{class:"input-icon"},[r("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2",stroke:"currentColor","stroke-width":"2"}),r("circle",{cx:"12",cy:"7",r:"4",stroke:"currentColor","stroke-width":"2"})])],-1))]),n.realName?(E(),R("span",P1,M(n.realName),1)):Y("",!0)]),r("div",R1,[_[16]||(_[16]=r("label",{for:"password",class:"form-label"},"密码",-1)),r("div",A1,[de(r("input",{id:"password","onUpdate:modelValue":_[3]||(_[3]=q=>s.password=q),type:i.value?"text":"password",class:ce(["form-input",{error:n.password}]),placeholder:"请输入密码",onBlur:y},null,42,M1),[[rs,s.password]]),_[15]||(_[15]=he('<span class="input-icon" data-v-2b12c018><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-2b12c018><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" data-v-2b12c018></rect><circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" data-v-2b12c018></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2" data-v-2b12c018></path></svg></span>',1)),r("button",{type:"button",class:"password-toggle",onClick:d},[i.value?(E(),R("svg",T1,_[13]||(_[13]=[r("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",stroke:"currentColor","stroke-width":"2"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},null,-1)]))):(E(),R("svg",O1,_[14]||(_[14]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",stroke:"currentColor","stroke-width":"2"},null,-1),r("line",{x1:"1",y1:"1",x2:"23",y2:"23",stroke:"currentColor","stroke-width":"2"},null,-1)])))])]),n.password?(E(),R("span",I1,M(n.password),1)):Y("",!0)]),r("div",V1,[_[20]||(_[20]=r("label",{for:"confirmPassword",class:"form-label"},"确认密码",-1)),r("div",U1,[de(r("input",{id:"confirmPassword","onUpdate:modelValue":_[4]||(_[4]=q=>s.confirmPassword=q),type:l.value?"text":"password",class:ce(["form-input",{error:n.confirmPassword}]),placeholder:"请再次输入密码",onBlur:w},null,42,N1),[[rs,s.confirmPassword]]),_[19]||(_[19]=he('<span class="input-icon" data-v-2b12c018><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-2b12c018><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" data-v-2b12c018></rect><circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" data-v-2b12c018></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2" data-v-2b12c018></path></svg></span>',1)),r("button",{type:"button",class:"password-toggle",onClick:u},[l.value?(E(),R("svg",j1,_[17]||(_[17]=[r("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",stroke:"currentColor","stroke-width":"2"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},null,-1)]))):(E(),R("svg",B1,_[18]||(_[18]=[r("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",stroke:"currentColor","stroke-width":"2"},null,-1),r("line",{x1:"1",y1:"1",x2:"23",y2:"23",stroke:"currentColor","stroke-width":"2"},null,-1)])))])]),n.confirmPassword?(E(),R("span",H1,M(n.confirmPassword),1)):Y("",!0)]),r("button",{type:"submit",class:"register-button",disabled:o.value},[o.value?(E(),R("span",D1)):Y("",!0),r("span",null,M(o.value?"注册中...":"创建管理员账户"),1)],8,F1)],32),r("div",K1,[r("div",z1,[X(j,{to:"/admin/login",class:"login-link"},{default:pe(()=>_[21]||(_[21]=[G("已有管理员账户？立即登录",-1)])),_:1,__:[21]}),X(j,{to:"/",class:"back-link"},{default:pe(()=>_[22]||(_[22]=[G("← 返回首页",-1)])),_:1,__:[22]})])])])])])])}}}),q1=ze(W1,[["__scopeId","data-v-2b12c018"]]),G1={class:"admin-dashboard"},Z1={class:"sidebar"},J1={class:"sidebar-nav"},Q1=["onClick"],Y1={class:"nav-icon"},X1={class:"nav-text"},ep={class:"sidebar-footer"},tp={class:"admin-info"},sp={class:"admin-avatar"},np={class:"admin-details"},op={class:"admin-name"},rp={class:"admin-role"},ip={class:"main-content"},lp={class:"top-header"},ap={class:"header-left"},cp={class:"page-title"},up={class:"header-right"},dp={class:"current-time"},fp={class:"content-area"},pp={key:0,class:"overview-content"},hp={class:"stats-grid"},vp={class:"stat-card"},gp={class:"stat-info"},mp={class:"stat-number"},wp={class:"stat-card"},bp={class:"stat-info"},yp={class:"stat-number"},_p={class:"stat-detail"},kp={class:"stat-card"},Cp={class:"stat-info"},xp={class:"stat-number"},$p={class:"stat-card"},Sp={class:"stat-info"},Ep={class:"stat-status"},Lp={class:"recent-activities"},Pp={class:"activity-list"},Rp={key:0,class:"no-activities"},Ap={class:"activity-time"},Mp={class:"activity-content"},Tp={key:1,class:"admins-content"},Op={class:"table-container"},Ip={class:"data-table"},Vp={class:"role-badge"},Up={class:"action-buttons"},Np=["onClick"],jp=["onClick"],Bp={key:2,class:"features-content"},Hp={class:"content-header"},Fp={class:"table-container"},Dp={class:"data-table"},Kp={class:"description-cell"},zp={class:"highlights-cell"},Wp={class:"action-buttons"},qp=["onClick"],Gp=["onClick"],Zp=["onClick"],Jp={key:3,class:"users-content"},Qp={class:"content-header"},Yp={class:"search-filters"},Xp={class:"table-container"},eh={class:"data-table"},th={class:"action-buttons"},sh=["onClick"],nh=["onClick"],oh={class:"pagination-container"},rh={class:"pagination-info"},ih={class:"pagination-controls"},lh=["disabled"],ah={class:"page-numbers"},ch=["onClick"],uh=["disabled"],dh={key:4,class:"placeholder-content"},fh={class:"modal-header"},ph={class:"form-group"},hh={class:"form-group"},vh={class:"form-group"},gh={class:"form-group"},mh={class:"form-row"},wh={class:"form-group"},bh={class:"form-group"},yh=["value"],_h={class:"modal-actions"},kh=["disabled"],Ch=Be({__name:"AdminDashboardView",setup(e){const t=rt(),s=W("overview"),n=W(""),o=W(null),i=W([]),l=W([]),c=W(!1),a=W(null),d=W(!1),u=W(""),p=W([]),g=W({totalUsers:0,activeUsers:0,inactiveUsers:0,todayRegistered:0}),v=xe({username:"",email:"",status:void 0}),y=xe({page:1,size:10,total:0}),w=W([]),O=[{value:1,label:"启用"},{value:0,label:"禁用"}],U=[{key:"overview",icon:"📊",label:"概览"},{key:"admins",icon:"👥",label:"管理员管理"},{key:"users",icon:"👤",label:"用户管理"},{key:"features",icon:"⭐",label:"核心功能管理"}],x=xe({adminCount:0,userCount:0,activeToday:0,systemStatus:"正常运行"}),_=xe({title:"",description:"",icon:"",highlights:[],sortOrder:1,status:1}),j=()=>{const k=new Date;n.value=k.toLocaleString("zh-CN")},q=()=>({overview:"系统概览",admins:"管理员管理",users:"用户管理",features:"核心功能管理"})[s.value]||"未知页面",ue=k=>new Date(k).toLocaleString("zh-CN"),ie=k=>new Date(k).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),F=()=>{const k=Te.getCurrentUser();k&&k.userType==="ADMIN"?o.value=k:t.push("/admin/login")},C=async()=>{try{const k=await Nf({page:1,size:100});k.success&&k.data&&(i.value=k.data.records,x.adminCount=k.data.total)}catch{}},be=async()=>{try{const k=await ku({page:1,size:100});k.success&&k.data&&(l.value=k.data.records)}catch{}},Q=async()=>{try{const k=await zf();k.success&&k.data&&(g.value=k.data,x.userCount=k.data.totalUsers,x.activeToday=k.data.todayRegistered)}catch{}},Je=async()=>{try{w.value=[]}catch{}},We=async k=>{try{const b=k.status===1?0:1;await jf(k.id,b),C()}catch{}},Ut=async k=>{if(confirm(`确定要删除管理员 ${k.realName} 吗？`))try{await Uf(k.id),C()}catch{}},St=()=>{Te.logout(),t.push("/admin/login")},_e=k=>{a.value=k,_.title=k.title,_.description=k.description,_.icon=k.icon,_.highlights=[...k.highlights],_.sortOrder=k.sortOrder,_.status=k.status,u.value=k.highlights.join(`
`),c.value=!0},te=()=>{c.value=!1,a.value=null,Object.assign(_,{title:"",description:"",icon:"",highlights:[],sortOrder:1,status:1}),u.value=""},se=async()=>{d.value=!0;try{_.highlights=u.value.split(`
`).filter(b=>b.trim());const k={title:_.title,description:_.description,icon:_.icon,highlights:_.highlights,sortOrder:_.sortOrder,status:_.status};a.value?await yu(a.value.id,k):await bu(k),te(),be()}catch{}finally{d.value=!1}},Qe=async k=>{try{const b=k.status===1?0:1;await Cu(k.id,b),be()}catch{}},lt=async k=>{if(confirm(`确定要删除核心功能 ${k.title} 吗？`))try{await _u(k.id),be()}catch{}},$e=async()=>{try{const k={page:y.page,size:y.size,username:v.username||void 0,email:v.email||void 0,status:v.status},b=await Ff(k);b.success&&b.data&&(p.value=b.data.records,y.total=b.data.total)}catch{}},Ee=async k=>{try{const b=k.status===1?0:1;await Df(k.id,b),$e(),Q()}catch{}},Et=async k=>{if(confirm(`确定要删除用户 ${k.username} 吗？`))try{await Kf(k.id),$e(),Q()}catch{}},Ye=()=>{y.page=1,$e()},Gt=()=>{Object.assign(v,{username:"",email:"",status:void 0}),y.page=1,$e()},Le=k=>{y.page=k,$e()},S=k=>{y.size=k,y.page=1,$e()},H=(k,b)=>{const L=[];let h=Math.max(1,k-Math.floor(2.5)),m=Math.min(b,h+5-1);m-h+1<5&&(h=Math.max(1,m-5+1));for(let $=h;$<=m;$++)L.push($);return L};return zt(()=>{F(),C(),be(),$e(),Q(),Je(),j();const k=setInterval(j,1e3);Os(()=>{clearInterval(k)})}),(k,b)=>(E(),R("div",G1,[r("aside",Z1,[b[16]||(b[16]=he('<div class="sidebar-header" data-v-23d2edf3><div class="logo" data-v-23d2edf3><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-23d2edf3><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-23d2edf3></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-23d2edf3></circle></svg></div><h2 class="brand-name" data-v-23d2edf3>管理后台</h2></div>',1)),r("nav",J1,[(E(),R(ae,null,et(U,L=>r("a",{key:L.key,href:"#",class:ce(["nav-item",{active:s.value===L.key}]),onClick:f=>s.value=L.key},[r("span",Y1,M(L.icon),1),r("span",X1,M(L.label),1)],10,Q1)),64))]),r("div",ep,[r("div",tp,[r("div",sp,M(o.value?.realName?.charAt(0)||"A"),1),r("div",np,[r("div",op,M(o.value?.realName),1),r("div",rp,M(o.value?.roleName),1)])]),r("button",{onClick:St,class:"logout-btn"},b[15]||(b[15]=[r("span",null,"退出登录",-1)]))])]),r("main",ip,[r("header",lp,[r("div",ap,[r("h1",cp,M(q()),1)]),r("div",up,[r("span",dp,M(n.value),1)])]),r("div",fp,[s.value==="overview"?(E(),R("div",pp,[r("div",hp,[r("div",vp,[b[18]||(b[18]=r("div",{class:"stat-icon"},"👥",-1)),r("div",gp,[b[17]||(b[17]=r("h3",null,"管理员总数",-1)),r("p",mp,M(x.adminCount),1)])]),r("div",wp,[b[20]||(b[20]=r("div",{class:"stat-icon"},"👤",-1)),r("div",bp,[b[19]||(b[19]=r("h3",null,"用户总数",-1)),r("p",yp,M(g.value.totalUsers),1),r("p",_p,"启用: "+M(g.value.activeUsers)+" | 禁用: "+M(g.value.inactiveUsers),1)])]),r("div",kp,[b[22]||(b[22]=r("div",{class:"stat-icon"},"📊",-1)),r("div",Cp,[b[21]||(b[21]=r("h3",null,"今日注册",-1)),r("p",xp,M(g.value.todayRegistered),1)])]),r("div",$p,[b[24]||(b[24]=r("div",{class:"stat-icon"},"🔄",-1)),r("div",Sp,[b[23]||(b[23]=r("h3",null,"系统状态",-1)),r("p",Ep,M(x.systemStatus),1)])])]),r("div",Lp,[b[26]||(b[26]=r("h3",null,"最近活动",-1)),r("div",Pp,[w.value.length===0?(E(),R("div",Rp,b[25]||(b[25]=[r("p",null,"暂无最近活动记录",-1)]))):(E(!0),R(ae,{key:1},et(w.value,L=>(E(),R("div",{key:L.id,class:"activity-item"},[r("div",Ap,M(ie(L.createTime)),1),r("div",Mp,M(L.content),1)]))),128))])])])):s.value==="admins"?(E(),R("div",Tp,[r("div",Op,[r("table",Ip,[b[27]||(b[27]=r("thead",null,[r("tr",null,[r("th",null,"编号"),r("th",null,"用户名"),r("th",null,"真实姓名"),r("th",null,"邮箱"),r("th",null,"角色"),r("th",null,"状态"),r("th",null,"创建时间"),r("th",null,"操作")])],-1)),r("tbody",null,[(E(!0),R(ae,null,et(i.value,L=>(E(),R("tr",{key:L.id},[r("td",null,M(L.id),1),r("td",null,M(L.username),1),r("td",null,M(L.realName),1),r("td",null,M(L.email),1),r("td",null,[r("span",Vp,M(L.roleName),1)]),r("td",null,[r("span",{class:ce(["status-badge",L.status===1?"active":"inactive"])},M(L.statusName),3)]),r("td",null,M(ue(L.createTime)),1),r("td",null,[r("div",Up,[r("button",{onClick:f=>We(L),class:"action-btn toggle"},M(L.status===1?"禁用":"启用"),9,Np),r("button",{onClick:f=>Ut(L),class:"action-btn delete"},"删除",8,jp)])])]))),128))])])])])):s.value==="features"?(E(),R("div",Bp,[r("div",Hp,[r("button",{onClick:b[0]||(b[0]=L=>c.value=!0),class:"create-btn"},b[28]||(b[28]=[r("span",null,"+ 新增核心功能",-1)]))]),r("div",Fp,[r("table",Dp,[b[29]||(b[29]=r("thead",null,[r("tr",null,[r("th",null,"编号"),r("th",null,"标题"),r("th",null,"描述"),r("th",null,"亮点"),r("th",null,"排序"),r("th",null,"状态"),r("th",null,"创建时间"),r("th",null,"操作")])],-1)),r("tbody",null,[(E(!0),R(ae,null,et(l.value,L=>(E(),R("tr",{key:L.id},[r("td",null,M(L.id),1),r("td",null,M(L.title),1),r("td",Kp,M(L.description),1),r("td",null,[r("div",zp,[(E(!0),R(ae,null,et(L.highlights,f=>(E(),R("span",{key:f,class:"highlight-tag"},M(f),1))),128))])]),r("td",null,M(L.sortOrder),1),r("td",null,[r("span",{class:ce(["status-badge",L.status===1?"active":"inactive"])},M(L.statusName),3)]),r("td",null,M(ue(L.createTime)),1),r("td",null,[r("div",Wp,[r("button",{onClick:f=>_e(L),class:"action-btn edit"},"编辑",8,qp),r("button",{onClick:f=>Qe(L),class:"action-btn toggle"},M(L.status===1?"禁用":"启用"),9,Gp),r("button",{onClick:f=>lt(L),class:"action-btn delete"},"删除",8,Zp)])])]))),128))])])])])):s.value==="users"?(E(),R("div",Jp,[r("div",Qp,[r("div",Yp,[de(r("input",{"onUpdate:modelValue":b[1]||(b[1]=L=>v.username=L),type:"text",placeholder:"搜索用户名...",class:"search-input",onKeyup:Yo(Ye,["enter"])},null,544),[[Ce,v.username]]),de(r("input",{"onUpdate:modelValue":b[2]||(b[2]=L=>v.email=L),type:"text",placeholder:"搜索邮箱...",class:"search-input",onKeyup:Yo(Ye,["enter"])},null,544),[[Ce,v.email]]),de(r("select",{"onUpdate:modelValue":b[3]||(b[3]=L=>v.status=L),class:"filter-select",onChange:Ye},b[30]||(b[30]=[r("option",{value:void 0},"全部状态",-1),r("option",{value:1},"启用",-1),r("option",{value:0},"禁用",-1)]),544),[[Gs,v.status]]),r("button",{onClick:Ye,class:"search-btn"},"搜索"),r("button",{onClick:Gt,class:"reset-btn"},"重置")])]),r("div",Xp,[r("table",eh,[b[31]||(b[31]=r("thead",null,[r("tr",null,[r("th",null,"编号"),r("th",null,"用户名"),r("th",null,"邮箱"),r("th",null,"状态"),r("th",null,"注册时间"),r("th",null,"操作")])],-1)),r("tbody",null,[(E(!0),R(ae,null,et(p.value,L=>(E(),R("tr",{key:L.id},[r("td",null,M(L.id),1),r("td",null,M(L.username),1),r("td",null,M(L.email),1),r("td",null,[r("span",{class:ce(["status-badge",L.status===1?"active":"inactive"])},M(L.statusName||(L.status===1?"启用":"禁用")),3)]),r("td",null,M(ue(L.createTime)),1),r("td",null,[r("div",th,[r("button",{onClick:f=>Ee(L),class:"action-btn toggle"},M(L.status===1?"禁用":"启用"),9,sh),r("button",{onClick:f=>Et(L),class:"action-btn delete"},"删除",8,nh)])])]))),128))])])]),r("div",oh,[r("div",rh," 共 "+M(y.total)+" 条记录，第 "+M(y.page)+" / "+M(Math.ceil(y.total/y.size))+" 页 ",1),r("div",ih,[de(r("select",{"onUpdate:modelValue":b[4]||(b[4]=L=>y.size=L),onChange:b[5]||(b[5]=L=>S(y.size)),class:"page-size-select"},b[32]||(b[32]=[r("option",{value:10},"10条/页",-1),r("option",{value:20},"20条/页",-1),r("option",{value:50},"50条/页",-1)]),544),[[Gs,y.size]]),r("button",{onClick:b[6]||(b[6]=L=>Le(y.page-1)),disabled:y.page<=1,class:"page-btn"}," 上一页 ",8,lh),r("span",ah,[(E(!0),R(ae,null,et(H(y.page,Math.ceil(y.total/y.size)),L=>(E(),R("button",{key:L,onClick:f=>Le(L),class:ce(["page-number",{active:L===y.page}])},M(L),11,ch))),128))]),r("button",{onClick:b[7]||(b[7]=L=>Le(y.page+1)),disabled:y.page>=Math.ceil(y.total/y.size),class:"page-btn"}," 下一页 ",8,uh)])])])):(E(),R("div",dh,[b[33]||(b[33]=r("div",{class:"placeholder-icon"},"🚧",-1)),b[34]||(b[34]=r("h3",null,"功能开发中",-1)),r("p",null,M(q())+"功能正在开发中，敬请期待！",1)]))])]),c.value?(E(),R("div",{key:0,class:"modal-overlay",onClick:te},[r("div",{class:"modal-content feature-modal",onClick:b[14]||(b[14]=is(()=>{},["stop"]))},[r("div",fh,[r("h3",null,M(a.value?"编辑核心功能":"新增核心功能"),1),r("button",{onClick:te,class:"close-btn"},"×")]),r("form",{onSubmit:is(se,["prevent"]),class:"modal-form"},[r("div",ph,[b[35]||(b[35]=r("label",null,"功能标题",-1)),de(r("input",{"onUpdate:modelValue":b[8]||(b[8]=L=>_.title=L),type:"text",required:""},null,512),[[Ce,_.title]])]),r("div",hh,[b[36]||(b[36]=r("label",null,"功能描述",-1)),de(r("textarea",{"onUpdate:modelValue":b[9]||(b[9]=L=>_.description=L),rows:"3",required:""},null,512),[[Ce,_.description]])]),r("div",vh,[b[37]||(b[37]=r("label",null,"功能图标 (SVG)",-1)),de(r("textarea",{"onUpdate:modelValue":b[10]||(b[10]=L=>_.icon=L),rows:"4",placeholder:"请输入SVG图标代码",required:""},null,512),[[Ce,_.icon]])]),r("div",gh,[b[38]||(b[38]=r("label",null,"功能亮点 (每行一个)",-1)),de(r("textarea",{"onUpdate:modelValue":b[11]||(b[11]=L=>u.value=L),rows:"3",placeholder:"请输入功能亮点，每行一个",required:""},null,512),[[Ce,u.value]])]),r("div",mh,[r("div",wh,[b[39]||(b[39]=r("label",null,"排序顺序",-1)),de(r("input",{"onUpdate:modelValue":b[12]||(b[12]=L=>_.sortOrder=L),type:"number",min:"1"},null,512),[[Ce,_.sortOrder,void 0,{number:!0}]])]),r("div",bh,[b[40]||(b[40]=r("label",null,"状态",-1)),de(r("select",{"onUpdate:modelValue":b[13]||(b[13]=L=>_.status=L),required:""},[(E(),R(ae,null,et(O,L=>r("option",{key:L.value,value:L.value},M(L.label),9,yh)),64))],512),[[Gs,_.status]])])]),r("div",_h,[r("button",{type:"button",onClick:te,class:"cancel-btn"},"取消"),r("button",{type:"submit",class:"submit-btn",disabled:d.value},M(d.value?"提交中...":a.value?"更新":"创建"),9,kh)])],32)])])):Y("",!0)]))}}),xh=ze(Ch,[["__scopeId","data-v-23d2edf3"]]),$h={class:"error-container"},Sh={class:"top-navbar"},Eh={class:"nav-content"},Lh={class:"nav-actions"},Ph={class:"welcome-text"},Rh={class:"error-main"},Ah={class:"error-content"},Mh={class:"error-actions"},Th=Be({__name:"Error404View",setup(e){const t=rt(),{user:s,isLoggedIn:n,logout:o,updateUserState:i}=Wt(),l=()=>{window.history.length>1?t.go(-1):t.push("/")},c=()=>{window.location.reload()},a=()=>{o(),i(),t.push("/")};return(d,u)=>{const p=ot("router-link");return E(),R("div",$h,[r("nav",Sh,[r("div",Eh,[u[2]||(u[2]=he('<div class="nav-brand" data-v-3ab86063><div class="brand-logo" data-v-3ab86063><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-3ab86063><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-3ab86063></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-3ab86063></circle></svg></div><span class="brand-name" data-v-3ab86063>膳食营养分析平台</span></div>',1)),r("div",Lh,[X(p,{to:"/",class:"nav-btn home-btn"},{default:pe(()=>u[0]||(u[0]=[G("返回首页",-1)])),_:1,__:[0]}),ye(n)?(E(),R(ae,{key:0},[r("span",Ph,"欢迎，"+M(ye(s)?.username),1),r("button",{onClick:a,class:"nav-btn logout-btn"},"退出登录")],64)):(E(),Kt(p,{key:1,to:"/login",class:"nav-btn login-btn"},{default:pe(()=>u[1]||(u[1]=[G("登录",-1)])),_:1,__:[1]}))])])]),r("main",Rh,[r("div",Ah,[u[6]||(u[6]=he('<div class="error-icon" data-v-3ab86063><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-3ab86063><text x="100" y="80" text-anchor="middle" class="error-number" data-v-3ab86063>404</text><circle cx="80" cy="130" r="15" stroke="currentColor" stroke-width="3" fill="none" data-v-3ab86063></circle><line x1="91" y1="141" x2="105" y2="155" stroke="currentColor" stroke-width="3" data-v-3ab86063></line><circle cx="130" cy="130" r="20" stroke="currentColor" stroke-width="3" fill="none" data-v-3ab86063></circle><path d="M125 125 Q130 120 135 125 Q135 130 130 135" stroke="currentColor" stroke-width="2" fill="none" data-v-3ab86063></path><circle cx="130" cy="145" r="2" fill="currentColor" data-v-3ab86063></circle></svg></div><div class="error-info" data-v-3ab86063><h1 class="error-title" data-v-3ab86063>页面未找到</h1><p class="error-description" data-v-3ab86063> 抱歉，您访问的页面不存在或已被移动。<br data-v-3ab86063> 请检查网址是否正确，或返回首页继续浏览。 </p><div class="error-reasons" data-v-3ab86063><h3 data-v-3ab86063>可能的原因：</h3><ul data-v-3ab86063><li data-v-3ab86063>网址输入错误</li><li data-v-3ab86063>页面已被删除或移动</li><li data-v-3ab86063>链接已过期</li><li data-v-3ab86063>您没有访问权限</li></ul></div></div>',2)),r("div",Mh,[X(p,{to:"/",class:"action-btn primary"},{default:pe(()=>u[3]||(u[3]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 返回首页 ",-1)])),_:1,__:[3]}),r("button",{onClick:l,class:"action-btn secondary"},u[4]||(u[4]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 返回上页 ",-1)])),r("button",{onClick:c,class:"action-btn tertiary"},u[5]||(u[5]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 刷新页面 ",-1)]))]),u[7]||(u[7]=r("div",{class:"help-info"},[r("p",null,"如果问题持续存在，请联系我们的技术支持团队"),r("a",{href:"mailto:<EMAIL>",class:"help-link"},"<EMAIL>")],-1))])])])}}}),Oh=ze(Th,[["__scopeId","data-v-3ab86063"]]),Ih={class:"error-container"},Vh={class:"top-navbar"},Uh={class:"nav-content"},Nh={class:"nav-actions"},jh={class:"welcome-text"},Bh={class:"error-main"},Hh={class:"error-content"},Fh={class:"error-info"},Dh={class:"error-description"},Kh={class:"permission-info"},zh={key:0,class:"permission-section"},Wh={key:1,class:"permission-section"},qh={key:2,class:"permission-section"},Gh={class:"error-actions"},Zh=Be({__name:"Error403View",setup(e){const t=rt(),{user:s,isLoggedIn:n,logout:o,updateUserState:i}=Wt(),l=we(()=>Ve.isUser()),c=we(()=>Ve.isAdmin()),a=()=>{if(n.value){if(l.value)return"此页面需要管理员权限，您当前是普通用户。";if(c.value)return"此页面仅限普通用户访问，请使用管理后台。"}else return"请先登录您的账户以获取相应权限。";return"您的账户权限不足以访问此页面。"},d=()=>c.value?"/admin/dashboard":l.value?"/dashboard":"/",u=()=>c.value?"前往管理后台":l.value?"前往用户控制台":"返回首页",p=()=>{window.history.length>1?t.go(-1):t.push("/")},g=()=>{o(),i(),t.push("/")};return(v,y)=>{const w=ot("router-link");return E(),R("div",Ih,[r("nav",Vh,[r("div",Uh,[y[2]||(y[2]=he('<div class="nav-brand" data-v-26c150c8><div class="brand-logo" data-v-26c150c8><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-26c150c8><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-26c150c8></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-26c150c8></circle></svg></div><span class="brand-name" data-v-26c150c8>膳食营养分析平台</span></div>',1)),r("div",Nh,[X(w,{to:"/",class:"nav-btn home-btn"},{default:pe(()=>y[0]||(y[0]=[G("返回首页",-1)])),_:1,__:[0]}),ye(n)?(E(),R(ae,{key:0},[r("span",jh,"欢迎，"+M(ye(s)?.username),1),r("button",{onClick:g,class:"nav-btn logout-btn"},"退出登录")],64)):(E(),Kt(w,{key:1,to:"/login",class:"nav-btn login-btn"},{default:pe(()=>y[1]||(y[1]=[G("登录",-1)])),_:1,__:[1]}))])])]),r("main",Bh,[r("div",Hh,[y[14]||(y[14]=he('<div class="error-icon" data-v-26c150c8><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-26c150c8><text x="100" y="80" text-anchor="middle" class="error-number" data-v-26c150c8>403</text><rect x="75" y="120" width="50" height="35" rx="5" stroke="currentColor" stroke-width="3" fill="none" data-v-26c150c8></rect><path d="M85 120 V110 C85 105 90 100 100 100 C110 100 115 105 115 110 V120" stroke="currentColor" stroke-width="3" fill="none" data-v-26c150c8></path><circle cx="100" cy="135" r="3" fill="currentColor" data-v-26c150c8></circle><circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3" data-v-26c150c8></circle><line x1="50" y1="50" x2="150" y2="150" stroke="currentColor" stroke-width="4" opacity="0.3" data-v-26c150c8></line></svg></div>',1)),r("div",Fh,[y[9]||(y[9]=r("h1",{class:"error-title"},"访问被拒绝",-1)),r("p",Dh,[y[3]||(y[3]=G(" 抱歉，您没有权限访问此页面。",-1)),y[4]||(y[4]=r("br",null,null,-1)),G(" "+M(a()),1)]),r("div",Kh,[y[8]||(y[8]=r("h3",null,"权限说明：",-1)),ye(n)?l.value?(E(),R("div",Wh,y[6]||(y[6]=[r("h4",null,"👤 普通用户权限",-1),r("p",null,"此页面仅限管理员访问，普通用户无法进入",-1)]))):c.value?(E(),R("div",qh,y[7]||(y[7]=[r("h4",null,"👨‍💼 管理员权限",-1),r("p",null,"此页面仅限普通用户访问，管理员请使用管理后台",-1)]))):Y("",!0):(E(),R("div",zh,y[5]||(y[5]=[r("h4",null,"🔐 未登录用户",-1),r("p",null,"您需要登录后才能访问此页面",-1)])))])]),r("div",Gh,[ye(n)?(E(),Kt(w,{key:1,to:d(),class:"action-btn primary"},{default:pe(()=>[y[12]||(y[12]=r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),G(" "+M(u()),1)]),_:1,__:[12]},8,["to"])):(E(),R(ae,{key:0},[X(w,{to:"/login",class:"action-btn primary"},{default:pe(()=>y[10]||(y[10]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M10 17L15 12L10 7M15 12H3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 用户登录 ",-1)])),_:1,__:[10]}),X(w,{to:"/admin/login",class:"action-btn secondary"},{default:pe(()=>y[11]||(y[11]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 管理员登录 ",-1)])),_:1,__:[11]})],64)),r("button",{onClick:p,class:"action-btn secondary"},y[13]||(y[13]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 返回上页 ",-1)]))]),y[15]||(y[15]=r("div",{class:"help-info"},[r("p",null,"如果您认为这是一个错误，请联系系统管理员"),r("a",{href:"mailto:<EMAIL>",class:"help-link"},"<EMAIL>")],-1))])])])}}}),Jh=ze(Zh,[["__scopeId","data-v-26c150c8"]]),Qh={class:"error-container"},Yh={class:"top-navbar"},Xh={class:"nav-content"},e2={class:"nav-actions"},t2={class:"welcome-text"},s2={class:"error-main"},n2={class:"error-content"},o2={class:"error-info"},r2={class:"error-details"},i2={class:"error-code"},l2={class:"code-value"},a2={class:"error-time"},c2={class:"time-value"},u2={key:0,class:"error-id"},d2={class:"id-value"},f2={class:"error-actions"},p2=Be({__name:"Error500View",setup(e){const t=rt(),{user:s,isLoggedIn:n,logout:o,updateUserState:i}=Wt(),l=W("HTTP 500"),c=W(""),a=W("");zt(()=>{c.value=new Date().toLocaleString("zh-CN"),a.value=d()});const d=()=>{const y=Date.now().toString(36),w=Math.random().toString(36).substr(2,5);return`ERR-${y}-${w}`.toUpperCase()},u=()=>{window.location.reload()},p=()=>{const y={errorCode:l.value,errorTime:c.value,errorId:a.value,userAgent:navigator.userAgent,url:window.location.href,userId:s.value?.id||"anonymous"};console.log("Error Report:",y),alert("感谢您的反馈！错误报告已提交，我们会尽快处理。")},g=()=>{alert("在线客服功能即将开放，请暂时使用邮箱或电话联系我们。")},v=()=>{o(),i(),t.push("/")};return(y,w)=>{const O=ot("router-link");return E(),R("div",Qh,[r("nav",Yh,[r("div",Xh,[w[2]||(w[2]=he('<div class="nav-brand" data-v-4f26ff27><div class="brand-logo" data-v-4f26ff27><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-4f26ff27><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-4f26ff27></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-4f26ff27></circle></svg></div><span class="brand-name" data-v-4f26ff27>膳食营养分析平台</span></div>',1)),r("div",e2,[X(O,{to:"/",class:"nav-btn home-btn"},{default:pe(()=>w[0]||(w[0]=[G("返回首页",-1)])),_:1,__:[0]}),ye(n)?(E(),R(ae,{key:0},[r("span",t2,"欢迎，"+M(ye(s)?.username),1),r("button",{onClick:v,class:"nav-btn logout-btn"},"退出登录")],64)):(E(),Kt(O,{key:1,to:"/login",class:"nav-btn login-btn"},{default:pe(()=>w[1]||(w[1]=[G("登录",-1)])),_:1,__:[1]}))])])]),r("main",s2,[r("div",n2,[w[16]||(w[16]=he('<div class="error-icon" data-v-4f26ff27><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-4f26ff27><text x="100" y="80" text-anchor="middle" class="error-number" data-v-4f26ff27>500</text><rect x="70" y="110" width="60" height="40" rx="4" stroke="currentColor" stroke-width="3" fill="none" data-v-4f26ff27></rect><rect x="75" y="115" width="50" height="30" rx="2" stroke="currentColor" stroke-width="2" fill="none" data-v-4f26ff27></rect><circle cx="85" cy="125" r="3" fill="#e74c3c" data-v-4f26ff27></circle><circle cx="95" cy="125" r="3" fill="#f39c12" data-v-4f26ff27></circle><circle cx="105" cy="125" r="3" fill="#e74c3c" data-v-4f26ff27></circle><line x1="110" y1="130" x2="120" y2="140" stroke="currentColor" stroke-width="2" data-v-4f26ff27></line><line x1="120" y1="130" x2="110" y2="140" stroke="currentColor" stroke-width="2" data-v-4f26ff27></line><rect x="75" y="150" width="50" height="8" rx="2" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3" data-v-4f26ff27></rect></svg></div>',1)),r("div",o2,[w[7]||(w[7]=r("h1",{class:"error-title"},"服务器内部错误",-1)),w[8]||(w[8]=r("p",{class:"error-description"},[G(" 抱歉，服务器遇到了一个意外错误，无法完成您的请求。"),r("br"),G(" 我们的技术团队已经收到通知，正在紧急处理此问题。 ")],-1)),r("div",r2,[w[6]||(w[6]=r("h3",null,"错误信息：",-1)),r("div",i2,[w[3]||(w[3]=r("span",{class:"code-label"},"错误代码:",-1)),r("span",l2,M(l.value),1)]),r("div",a2,[w[4]||(w[4]=r("span",{class:"time-label"},"发生时间:",-1)),r("span",c2,M(c.value),1)]),a.value?(E(),R("div",u2,[w[5]||(w[5]=r("span",{class:"id-label"},"错误ID:",-1)),r("span",d2,M(a.value),1)])):Y("",!0)]),w[9]||(w[9]=r("div",{class:"error-solutions"},[r("h3",null,"您可以尝试："),r("ul",null,[r("li",null,"刷新页面重新加载"),r("li",null,"稍后再试（服务器可能正在维护）"),r("li",null,"清除浏览器缓存和Cookie"),r("li",null,"检查网络连接是否正常"),r("li",null,"联系技术支持获取帮助")])],-1))]),r("div",f2,[r("button",{onClick:u,class:"action-btn primary"},w[10]||(w[10]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 刷新页面 ",-1)])),X(O,{to:"/",class:"action-btn secondary"},{default:pe(()=>w[11]||(w[11]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 返回首页 ",-1)])),_:1,__:[11]}),r("button",{onClick:p,class:"action-btn tertiary"},w[12]||(w[12]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M12 7V13M12 17H12.01",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 报告错误 ",-1)]))]),r("div",{class:"contact-info"},[w[15]||(w[15]=r("h3",null,"需要帮助？",-1)),r("div",{class:"contact-methods"},[w[14]||(w[14]=he('<div class="contact-item" data-v-4f26ff27><span class="contact-label" data-v-4f26ff27>技术支持邮箱:</span><a href="mailto:<EMAIL>" class="contact-link" data-v-4f26ff27><EMAIL></a></div><div class="contact-item" data-v-4f26ff27><span class="contact-label" data-v-4f26ff27>客服热线:</span><a href="tel:************" class="contact-link" data-v-4f26ff27>************</a></div>',2)),r("div",{class:"contact-item"},[w[13]||(w[13]=r("span",{class:"contact-label"},"在线客服:",-1)),r("button",{onClick:g,class:"contact-link-btn"},"点击咨询")])])])])])])}}}),h2=ze(p2,[["__scopeId","data-v-4f26ff27"]]),v2={class:"error-container"},g2={class:"top-navbar"},m2={class:"nav-content"},w2={class:"nav-actions"},b2={class:"welcome-text"},y2={class:"error-main"},_2={class:"error-content"},k2={class:"error-info"},C2={class:"error-description"},x2={class:"network-status"},$2={class:"status-item"},S2={class:"status-item"},E2={class:"status-value"},L2={class:"status-item"},P2={key:0,class:"status-item"},R2={class:"status-value"},A2={class:"error-actions"},M2=["disabled"],T2={key:0,class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},O2={key:1,class:"btn-icon spinning",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},I2=["disabled"],V2={key:0,class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},U2={key:1,class:"btn-icon spinning",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},N2=Be({__name:"ErrorNetworkView",setup(e){const t=rt(),{user:s,isLoggedIn:n,logout:o,updateUserState:i}=Wt(),l=W(!1),c=W(!1),a=xe({online:navigator.onLine,type:u()}),d=xe({reachable:!1,responseTime:0});function u(){const U=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return U?U.effectiveType||U.type||"未知":navigator.onLine?"已连接":"离线"}const p=()=>{if(a.online){if(!d.reachable)return"网络连接正常，但无法访问服务器。"}else return"您的设备似乎已断开网络连接。";return"网络连接出现问题，请稍后重试。"},g=async()=>{l.value=!0;try{a.online=navigator.onLine,a.type=u();const U=Date.now();try{const x=await fetch("/api/health",{method:"GET",cache:"no-cache",signal:AbortSignal.timeout(5e3)}),_=Date.now();d.responseTime=_-U,d.reachable=x.ok}catch{d.reachable=!1,d.responseTime=Date.now()-U}await new Promise(x=>setTimeout(x,1e3))}finally{l.value=!1}},v=async()=>{c.value=!0;try{await g(),a.online&&d.reachable&&window.location.reload()}finally{c.value=!1}},y=()=>{a.online=!0,a.type=u()},w=()=>{a.online=!1,a.type="离线"},O=()=>{o(),i(),t.push("/")};return zt(()=>{window.addEventListener("online",y),window.addEventListener("offline",w),g()}),Os(()=>{window.removeEventListener("online",y),window.removeEventListener("offline",w)}),(U,x)=>{const _=ot("router-link");return E(),R("div",v2,[r("nav",g2,[r("div",m2,[x[2]||(x[2]=he('<div class="nav-brand" data-v-0eca0535><div class="brand-logo" data-v-0eca0535><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-0eca0535><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-0eca0535></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-0eca0535></circle></svg></div><span class="brand-name" data-v-0eca0535>膳食营养分析平台</span></div>',1)),r("div",w2,[X(_,{to:"/",class:"nav-btn home-btn"},{default:pe(()=>x[0]||(x[0]=[G("返回首页",-1)])),_:1,__:[0]}),ye(n)?(E(),R(ae,{key:0},[r("span",b2,"欢迎，"+M(ye(s)?.username),1),r("button",{onClick:O,class:"nav-btn logout-btn"},"退出登录")],64)):(E(),Kt(_,{key:1,to:"/login",class:"nav-btn login-btn"},{default:pe(()=>x[1]||(x[1]=[G("登录",-1)])),_:1,__:[1]}))])])]),r("main",y2,[r("div",_2,[x[17]||(x[17]=he('<div class="error-icon" data-v-0eca0535><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-0eca0535><circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="3" fill="none" opacity="0.3" data-v-0eca0535></circle><path d="M60 120 Q100 80 140 120" stroke="currentColor" stroke-width="4" fill="none" opacity="0.5" data-v-0eca0535></path><path d="M70 130 Q100 100 130 130" stroke="currentColor" stroke-width="4" fill="none" opacity="0.7" data-v-0eca0535></path><path d="M80 140 Q100 120 120 140" stroke="currentColor" stroke-width="4" fill="none" data-v-0eca0535></path><line x1="85" y1="85" x2="115" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round" data-v-0eca0535></line><line x1="115" y1="85" x2="85" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round" data-v-0eca0535></line><rect x="90" y="145" width="20" height="15" rx="2" stroke="currentColor" stroke-width="2" fill="none" data-v-0eca0535></rect><circle cx="100" cy="152" r="2" fill="currentColor" data-v-0eca0535></circle></svg></div>',1)),r("div",k2,[x[10]||(x[10]=r("h1",{class:"error-title"},"网络连接失败",-1)),r("p",C2,[x[3]||(x[3]=G(" 无法连接到服务器，请检查您的网络连接。",-1)),x[4]||(x[4]=r("br",null,null,-1)),G(" "+M(p()),1)]),r("div",x2,[x[9]||(x[9]=r("h3",null,"网络状态检测：",-1)),r("div",$2,[x[5]||(x[5]=r("span",{class:"status-label"},"网络连接:",-1)),r("span",{class:ce(["status-value",a.online?"status-online":"status-offline"])},M(a.online?"已连接":"已断开"),3)]),r("div",S2,[x[6]||(x[6]=r("span",{class:"status-label"},"连接类型:",-1)),r("span",E2,M(a.type),1)]),r("div",L2,[x[7]||(x[7]=r("span",{class:"status-label"},"服务器状态:",-1)),r("span",{class:ce(["status-value",d.reachable?"status-online":"status-offline"])},M(d.reachable?"可访问":"无法访问"),3)]),d.reachable?Y("",!0):(E(),R("div",P2,[x[8]||(x[8]=r("span",{class:"status-label"},"响应时间:",-1)),r("span",R2,M(d.responseTime)+"ms",1)]))]),x[11]||(x[11]=r("div",{class:"network-solutions"},[r("h3",null,"解决方案："),r("ul",null,[r("li",null,"检查网络连接是否正常"),r("li",null,"尝试刷新页面或重新加载"),r("li",null,"检查WiFi或移动数据连接"),r("li",null,"重启路由器或调制解调器"),r("li",null,"联系网络服务提供商"),r("li",null,"检查防火墙或代理设置")])],-1))]),r("div",A2,[r("button",{onClick:g,disabled:l.value,class:"action-btn primary"},[l.value?(E(),R("svg",O2,x[13]||(x[13]=[r("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(E(),R("svg",T2,x[12]||(x[12]=[r("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),G(" "+M(l.value?"检测中...":"检测网络"),1)],8,M2),r("button",{onClick:v,disabled:c.value,class:"action-btn secondary"},[c.value?(E(),R("svg",U2,x[15]||(x[15]=[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4",fill:"none",opacity:"0.3"},null,-1),r("path",{d:"M12 2A10 10 0 0 1 22 12",stroke:"currentColor","stroke-width":"4",fill:"none","stroke-linecap":"round"},null,-1)]))):(E(),R("svg",V2,x[14]||(x[14]=[r("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),r("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),G(" "+M(c.value?"重试中...":"重试连接"),1)],8,I2),X(_,{to:"/",class:"action-btn tertiary"},{default:pe(()=>x[16]||(x[16]=[r("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[r("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),r("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),G(" 返回首页 ",-1)])),_:1,__:[16]})]),x[18]||(x[18]=he('<div class="diagnostic-info" data-v-0eca0535><h3 data-v-0eca0535>网络诊断建议：</h3><div class="diagnostic-steps" data-v-0eca0535><div class="diagnostic-step" data-v-0eca0535><span class="step-number" data-v-0eca0535>1</span><div class="step-content" data-v-0eca0535><h4 data-v-0eca0535>检查基础连接</h4><p data-v-0eca0535>确认设备已连接到WiFi或移动网络，信号强度良好</p></div></div><div class="diagnostic-step" data-v-0eca0535><span class="step-number" data-v-0eca0535>2</span><div class="step-content" data-v-0eca0535><h4 data-v-0eca0535>测试其他网站</h4><p data-v-0eca0535>尝试访问其他网站，确认是否为全局网络问题</p></div></div><div class="diagnostic-step" data-v-0eca0535><span class="step-number" data-v-0eca0535>3</span><div class="step-content" data-v-0eca0535><h4 data-v-0eca0535>重启网络设备</h4><p data-v-0eca0535>重启路由器、调制解调器或移动设备的网络连接</p></div></div></div></div>',1))])])])}}}),j2=ze(N2,[["__scopeId","data-v-0eca0535"]]),Xe=pu({history:Fc("/"),routes:[{path:"/",name:"home",component:zu,beforeEnter:wr,meta:{title:"膳食营养分析平台 - 智能营养管理"}},{path:"/login",name:"login",component:Ad,beforeEnter:Hs,meta:{title:"登录 - 膳食营养分析平台"}},{path:"/register",name:"register",component:_f,beforeEnter:Hs,meta:{title:"注册 - 膳食营养分析平台"}},{path:"/dashboard",name:"dashboard",component:Of,beforeEnter:(e,t,s)=>{wr(e,t,n=>{n?s(n):qu(["USER"])(e,t,s)})},meta:{title:"控制台 - 膳食营养分析平台"}},{path:"/admin/login",name:"admin-login",component:f1,beforeEnter:Hs,meta:{title:"管理员登录 - 膳食营养分析平台"}},{path:"/admin/register",name:"admin-register",component:q1,beforeEnter:Hs,meta:{title:"管理员注册 - 膳食营养分析平台"}},{path:"/admin/dashboard",name:"admin-dashboard",component:xh,beforeEnter:Wu,meta:{title:"管理后台 - 膳食营养分析平台"}},{path:"/error/403",name:"error-403",component:Jh,meta:{title:"访问被拒绝 - 膳食营养分析平台"}},{path:"/error/500",name:"error-500",component:h2,meta:{title:"服务器错误 - 膳食营养分析平台"}},{path:"/error/network",name:"error-network",component:j2,meta:{title:"网络错误 - 膳食营养分析平台"}},{path:"/:pathMatch(.*)*",name:"error-404",component:Oh,meta:{title:"页面未找到 - 膳食营养分析平台"}}]});Xe.beforeEach(e=>{e.meta.title&&(document.title=e.meta.title)});class Js{static handleHttpError(t,s){const n={type:"UNKNOWN",message:"发生未知错误",timestamp:new Date};if(s)switch(n.code=s.status,s.status){case 403:n.type="PERMISSION",n.message="访问被拒绝，权限不足",Xe.push("/error/403");return;case 404:n.type="NOT_FOUND",n.message="请求的资源不存在",Xe.push("/error/404");return;case 500:case 502:case 503:case 504:n.type="SERVER",n.message="服务器内部错误",Xe.push("/error/500");return;default:if(s.status>=400&&s.status<500)n.type="PERMISSION",n.message="客户端请求错误";else if(s.status>=500){n.type="SERVER",n.message="服务器错误",Xe.push("/error/500");return}}else if(t){if(t.name==="TypeError"&&t.message.includes("fetch")){n.type="NETWORK",n.message="网络连接失败",Xe.push("/error/network");return}else if(t.name==="AbortError"){n.type="NETWORK",n.message="请求超时",Xe.push("/error/network");return}}this.logError(n)}static handleNetworkError(t){const s={type:"NETWORK",message:"网络连接失败",details:t,timestamp:new Date};this.logError(s),Xe.push("/error/network")}static handlePermissionError(t){const s={type:"PERMISSION",code:403,message:t||"访问被拒绝",timestamp:new Date};this.logError(s),Xe.push("/error/403")}static handleServerError(t,s){const n={type:"SERVER",code:t||500,message:s||"服务器内部错误",timestamp:new Date};this.logError(n),Xe.push("/error/500")}static logError(t){console.error("[GlobalErrorHandler]",t),this.reportError(t)}static reportError(t){try{const s={type:t.type,code:t.code,message:t.message,details:t.details,timestamp:t.timestamp.toISOString(),userAgent:navigator.userAgent,url:window.location.href,userId:this.getCurrentUserId()};fetch("/api/error-report",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).catch(()=>{})}catch{}}static getCurrentUserId(){try{const t=localStorage.getItem("user_info");if(t)return JSON.parse(t).id||null}catch{}return null}}function B2(){window.addEventListener("unhandledrejection",e=>{if(console.error("Unhandled promise rejection:",e.reason),e.reason&&typeof e.reason=="object"&&e.reason.name==="TypeError"&&e.reason.message.includes("fetch")){Js.handleNetworkError(e.reason),e.preventDefault();return}Js.handleHttpError(e.reason),e.preventDefault()}),window.addEventListener("error",e=>{console.error("Global error:",e.error);const t={type:"UNKNOWN",message:e.message||"发生未知错误",details:{filename:e.filename,lineno:e.lineno,colno:e.colno,error:e.error},timestamp:new Date};Js.logError(t)})}function H2(){window.addEventListener("online",()=>{console.log("Network connection restored")}),window.addEventListener("offline",()=>{console.log("Network connection lost"),Js.handleNetworkError(new Error("Network connection lost"))})}const mo=nc(dc);B2();H2();mo.use(lc());mo.use(Xe);mo.mount("#app");
