package com.example.meals.config;

import com.example.meals.interceptor.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web 配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private JwtInterceptor jwtInterceptor;
    
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                // 需要JWT认证的路径
                .addPathPatterns("/api/**")
                // 排除不需要认证的路径
                .excludePathPatterns(
                        "/api/user/login",                    // 用户登录
                        "/api/user/login-with-code",          // 邮箱验证码登录
                        "/api/user/register",                 // 用户注册
                        "/api/user/check-username",           // 检查用户名
                        "/api/user/check-email",              // 检查邮箱
                        "/api/user/check-phone",              // 检查手机号
                        "/api/user/send-verification-code",   // 发送邮箱验证码
                        "/api/user/verify-code",              // 验证邮箱验证码
                        "/api/user/check-verification-code",  // 检查验证码状态
                        "/api/admin/login",                   // 管理员登录
                        "/api/admin/register",                // 管理员注册
                        "/api/admin/check-username",          // 检查管理员用户名
                        "/api/admin/check-email",             // 检查管理员邮箱
                        "/api/core-features/enabled"          // 获取启用的核心功能（公开接口）
                );
    }
    
    @Override
    public void addCorsMappings(@NonNull CorsRegistry registry) {
        registry.addMapping("/**")
                // 使用allowedOriginPatterns支持通配符，同时支持凭据
                .allowedOriginPatterns(
                    "http://localhost:*",
                    "http://127.0.0.1:*"
                )
                // 允许的HTTP方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                // 允许的请求头
                .allowedHeaders("*")
                // 允许凭据
                .allowCredentials(true)
                // 暴露的响应头
                .exposedHeaders("Authorization", "Content-Type")
                // 预检请求缓存时间
                .maxAge(3600);
    }
}
