<template>
  <div class="error-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <router-link to="/" class="nav-btn home-btn">返回首页</router-link>
          <template v-if="isLoggedIn">
            <span class="welcome-text">欢迎，{{ user?.username }}</span>
            <button @click="handleLogout" class="nav-btn logout-btn">退出登录</button>
          </template>
          <template v-else>
            <router-link to="/login" class="nav-btn login-btn">登录</router-link>
          </template>
        </div>
      </div>
    </nav>

    <!-- 错误内容区域 -->
    <main class="error-main">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 403数字 -->
            <text x="100" y="80" text-anchor="middle" class="error-number">403</text>
            <!-- 锁图标 -->
            <rect x="75" y="120" width="50" height="35" rx="5" stroke="currentColor" stroke-width="3" fill="none"/>
            <path d="M85 120 V110 C85 105 90 100 100 100 C110 100 115 105 115 110 V120" stroke="currentColor" stroke-width="3" fill="none"/>
            <circle cx="100" cy="135" r="3" fill="currentColor"/>
            <!-- 禁止符号 -->
            <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
            <line x1="50" y1="50" x2="150" y2="150" stroke="currentColor" stroke-width="4" opacity="0.3"/>
          </svg>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">访问被拒绝</h1>
          <p class="error-description">
            抱歉，您没有权限访问此页面。<br>
            {{ getPermissionMessage() }}
          </p>
          
          <!-- 权限说明 -->
          <div class="permission-info">
            <h3>权限说明：</h3>
            <div v-if="!isLoggedIn" class="permission-section">
              <h4>🔐 未登录用户</h4>
              <p>您需要登录后才能访问此页面</p>
            </div>
            <div v-else-if="isUser" class="permission-section">
              <h4>👤 普通用户权限</h4>
              <p>此页面仅限管理员访问，普通用户无法进入</p>
            </div>
            <div v-else-if="isAdmin" class="permission-section">
              <h4>👨‍💼 管理员权限</h4>
              <p>此页面仅限普通用户访问，管理员请使用管理后台</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <template v-if="!isLoggedIn">
            <router-link to="/login" class="action-btn primary">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 17L15 12L10 7M15 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              用户登录
            </router-link>
            
            <router-link to="/admin/login" class="action-btn secondary">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              管理员登录
            </router-link>
          </template>
          
          <template v-else>
            <router-link :to="getRedirectPath()" class="action-btn primary">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              {{ getRedirectText() }}
            </router-link>
          </template>
          
          <button @click="goBack" class="action-btn secondary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回上页
          </button>
        </div>

        <!-- 帮助信息 -->
        <div class="help-info">
          <p>如果您认为这是一个错误，请联系系统管理员</p>
          <a href="mailto:<EMAIL>" class="help-link"><EMAIL></a>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { UserManager } from '../utils/auth'

const router = useRouter()
const { user, isLoggedIn, logout, updateUserState } = useAuth()

// 计算用户类型
const isUser = computed(() => UserManager.isUser())
const isAdmin = computed(() => UserManager.isAdmin())

// 获取权限提示信息
const getPermissionMessage = () => {
  if (!isLoggedIn.value) {
    return '请先登录您的账户以获取相应权限。'
  } else if (isUser.value) {
    return '此页面需要管理员权限，您当前是普通用户。'
  } else if (isAdmin.value) {
    return '此页面仅限普通用户访问，请使用管理后台。'
  }
  return '您的账户权限不足以访问此页面。'
}

// 获取重定向路径
const getRedirectPath = () => {
  if (isAdmin.value) {
    return '/admin/dashboard'
  } else if (isUser.value) {
    return '/dashboard'
  }
  return '/'
}

// 获取重定向按钮文字
const getRedirectText = () => {
  if (isAdmin.value) {
    return '前往管理后台'
  } else if (isUser.value) {
    return '前往用户控制台'
  }
  return '返回首页'
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}
</script>

<style scoped>
/* CSS变量定义 - 与平台保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

.error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 顶部导航栏 - 与现有页面保持一致 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-btn {
  padding: 0.5rem 1.25rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.nav-btn.home-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: transparent;
}

.nav-btn.home-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.28);
}

.nav-btn.login-btn {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nav-btn.login-btn:hover {
  background: var(--primary-color);
  color: white;
}

.nav-btn.logout-btn {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.nav-btn.logout-btn:hover {
  background: var(--accent-color);
  color: white;
}

.welcome-text {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 错误内容区域 */
.error-main {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 2rem;
}

.error-content {
  max-width: 600px;
  text-align: center;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.error-icon {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  color: var(--primary-color);
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.action-btn.secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.action-btn.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.help-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.help-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.help-link:hover {
  text-decoration: underline;
}

/* 403特有的样式覆盖 */
.error-number {
  fill: #e74c3c; /* 使用红色表示权限错误 */
}

.error-icon {
  color: #e74c3c;
}

.permission-info {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.permission-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.permission-section {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--background-white);
  border-radius: var(--radius-sm);
  border-left: 4px solid var(--accent-color);
}

.permission-section:last-child {
  margin-bottom: 0;
}

.permission-section h4 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.permission-section p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* 权限相关的按钮样式 */
.action-btn.primary {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  .nav-btn {
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
  }

  .error-main {
    padding: 1rem;
  }

  .error-content {
    padding: 2rem 1.5rem;
  }

  .error-title {
    font-size: 2rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .permission-section {
    padding: 0.75rem;
  }
}
</style>
