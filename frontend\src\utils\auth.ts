// JWT认证工具类

const TOKEN_KEY = 'auth_token'
const USER_KEY = 'user_info'

/**
 * Token管理工具
 */
export class TokenManager {
  
  /**
   * 保存Token
   */
  static setToken(token: string): void {
    localStorage.setItem(TOKEN_KEY, token)
  }
  
  /**
   * 获取Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY)
  }
  
  /**
   * 移除Token
   */
  static removeToken(): void {
    localStorage.removeItem(TOKEN_KEY)
  }
  
  /**
   * 检查Token是否存在
   */
  static hasToken(): boolean {
    return !!this.getToken()
  }
  
  /**
   * 获取Authorization头
   */
  static getAuthHeader(): string | null {
    const token = this.getToken()
    return token ? `Bearer ${token}` : null
  }
}

/**
 * 用户信息管理工具
 */
export class UserManager {
  
  /**
   * 保存用户信息
   */
  static setUser(user: any): void {
    localStorage.setItem(USER_KEY, JSON.stringify(user))
  }
  
  /**
   * 获取用户信息
   */
  static getUser(): any | null {
    const userStr = localStorage.getItem(USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch (error) {
        // 解析用户数据失败时清除数据
        this.removeUser()
        return null
      }
    }
    return null
  }
  
  /**
   * 移除用户信息
   */
  static removeUser(): void {
    localStorage.removeItem(USER_KEY)
  }
  
  /**
   * 检查用户是否已登录
   */
  static isLoggedIn(): boolean {
    return !!this.getUser() && TokenManager.hasToken()
  }
  
  /**
   * 获取用户类型
   */
  static getUserType(): string | null {
    const user = this.getUser()
    return user?.userType || null
  }
  
  /**
   * 检查是否为管理员
   */
  static isAdmin(): boolean {
    return this.getUserType() === 'ADMIN'
  }
  
  /**
   * 检查是否为普通用户
   */
  static isUser(): boolean {
    return this.getUserType() === 'USER'
  }
}

/**
 * 认证管理器
 */
export class AuthManager {
  
  /**
   * 登录
   */
  static login(token: string, user: any): void {
    TokenManager.setToken(token)
    UserManager.setUser(user)
  }
  
  /**
   * 登出
   */
  static logout(): void {
    TokenManager.removeToken()
    UserManager.removeUser()
  }
  
  /**
   * 检查认证状态
   */
  static isAuthenticated(): boolean {
    return UserManager.isLoggedIn()
  }
  
  /**
   * 获取当前用户
   */
  static getCurrentUser(): any | null {
    return UserManager.getUser()
  }
  
  /**
   * 获取认证头
   */
  static getAuthHeader(): string | null {
    return TokenManager.getAuthHeader()
  }
}

/**
 * HTTP请求拦截器
 */
export function createAuthenticatedRequest() {
  return async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const authHeader = AuthManager.getAuthHeader()

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      // 如果返回401，说明token过期或无效，自动登出
      if (response.status === 401) {
        AuthManager.logout()
        // 重定向到登录页面
        window.location.href = '/login'
        return response
      }

      // 如果返回403，重定向到权限错误页面
      if (response.status === 403) {
        window.location.href = '/error/403'
        return response
      }

      // 如果返回5xx错误，重定向到服务器错误页面
      if (response.status >= 500) {
        window.location.href = '/error/500'
        return response
      }

      return response
    } catch (error) {
      // 网络错误处理
      if (error instanceof TypeError && error.message.includes('fetch')) {
        window.location.href = '/error/network'
      }
      throw error
    }
  }
}

// 创建认证请求实例
export const authFetch = createAuthenticatedRequest()

/**
 * API响应处理工具
 */
export async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  const result = await response.json()
  
  if (!result.success) {
    throw new Error(result.message || 'API request failed')
  }
  
  return result.data
}

/**
 * 带认证的API请求工具
 */
export async function apiRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await authFetch(url, options)
  return handleApiResponse<T>(response)
}
