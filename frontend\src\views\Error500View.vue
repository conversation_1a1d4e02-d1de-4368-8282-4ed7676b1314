<template>
  <div class="error-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <router-link to="/" class="nav-btn home-btn">返回首页</router-link>
          <template v-if="isLoggedIn">
            <span class="welcome-text">欢迎，{{ user?.username }}</span>
            <button @click="handleLogout" class="nav-btn logout-btn">退出登录</button>
          </template>
          <template v-else>
            <router-link to="/login" class="nav-btn login-btn">登录</router-link>
          </template>
        </div>
      </div>
    </nav>

    <!-- 错误内容区域 -->
    <main class="error-main">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 500数字 -->
            <text x="100" y="80" text-anchor="middle" class="error-number">500</text>
            <!-- 服务器图标 -->
            <rect x="70" y="110" width="60" height="40" rx="4" stroke="currentColor" stroke-width="3" fill="none"/>
            <rect x="75" y="115" width="50" height="30" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
            <!-- 错误指示灯 -->
            <circle cx="85" cy="125" r="3" fill="#e74c3c"/>
            <circle cx="95" cy="125" r="3" fill="#f39c12"/>
            <circle cx="105" cy="125" r="3" fill="#e74c3c"/>
            <!-- 错误符号 -->
            <line x1="110" y1="130" x2="120" y2="140" stroke="currentColor" stroke-width="2"/>
            <line x1="120" y1="130" x2="110" y2="140" stroke="currentColor" stroke-width="2"/>
            <!-- 服务器底座 -->
            <rect x="75" y="150" width="50" height="8" rx="2" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3"/>
          </svg>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">服务器内部错误</h1>
          <p class="error-description">
            抱歉，服务器遇到了一个意外错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在紧急处理此问题。
          </p>
          
          <!-- 错误详情 -->
          <div class="error-details">
            <h3>错误信息：</h3>
            <div class="error-code">
              <span class="code-label">错误代码:</span>
              <span class="code-value">{{ errorCode }}</span>
            </div>
            <div class="error-time">
              <span class="time-label">发生时间:</span>
              <span class="time-value">{{ errorTime }}</span>
            </div>
            <div v-if="errorId" class="error-id">
              <span class="id-label">错误ID:</span>
              <span class="id-value">{{ errorId }}</span>
            </div>
          </div>

          <!-- 可能的解决方案 -->
          <div class="error-solutions">
            <h3>您可以尝试：</h3>
            <ul>
              <li>刷新页面重新加载</li>
              <li>稍后再试（服务器可能正在维护）</li>
              <li>清除浏览器缓存和Cookie</li>
              <li>检查网络连接是否正常</li>
              <li>联系技术支持获取帮助</li>
            </ul>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <button @click="refreshPage" class="action-btn primary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            刷新页面
          </button>
          
          <router-link to="/" class="action-btn secondary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回首页
          </router-link>

          <button @click="reportError" class="action-btn tertiary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 7V13M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            报告错误
          </button>
        </div>

        <!-- 联系信息 -->
        <div class="contact-info">
          <h3>需要帮助？</h3>
          <div class="contact-methods">
            <div class="contact-item">
              <span class="contact-label">技术支持邮箱:</span>
              <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
            </div>
            <div class="contact-item">
              <span class="contact-label">客服热线:</span>
              <a href="tel:************" class="contact-link">************</a>
            </div>
            <div class="contact-item">
              <span class="contact-label">在线客服:</span>
              <button @click="openLiveChat" class="contact-link-btn">点击咨询</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const router = useRouter()
const { user, isLoggedIn, logout, updateUserState } = useAuth()

// 错误信息
const errorCode = ref('HTTP 500')
const errorTime = ref('')
const errorId = ref('')

// 初始化错误信息
onMounted(() => {
  errorTime.value = new Date().toLocaleString('zh-CN')
  errorId.value = generateErrorId()
})

// 生成错误ID
const generateErrorId = () => {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `ERR-${timestamp}-${random}`.toUpperCase()
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 报告错误
const reportError = () => {
  const errorReport = {
    errorCode: errorCode.value,
    errorTime: errorTime.value,
    errorId: errorId.value,
    userAgent: navigator.userAgent,
    url: window.location.href,
    userId: user.value?.id || 'anonymous'
  }
  
  // 这里可以发送错误报告到服务器
  console.log('Error Report:', errorReport)
  
  // 显示感谢信息
  alert('感谢您的反馈！错误报告已提交，我们会尽快处理。')
}

// 打开在线客服
const openLiveChat = () => {
  // 这里可以集成在线客服系统
  alert('在线客服功能即将开放，请暂时使用邮箱或电话联系我们。')
}

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}
</script>

<style scoped>
/* CSS变量定义 - 与平台保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

.error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 顶部导航栏样式 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-btn {
  padding: 0.5rem 1.25rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.nav-btn.home-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: transparent;
}

.nav-btn.home-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.28);
}

.nav-btn.login-btn {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nav-btn.login-btn:hover {
  background: var(--primary-color);
  color: white;
}

.nav-btn.logout-btn {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.nav-btn.logout-btn:hover {
  background: var(--accent-color);
  color: white;
}

.welcome-text {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 错误内容区域 */
.error-main {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 2rem;
}

.error-content {
  max-width: 600px;
  text-align: center;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.error-icon {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  color: var(--primary-color);
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.action-btn.secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.action-btn.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.action-btn.tertiary {
  background: var(--background-light);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.action-btn.tertiary:hover {
  background: var(--background-white);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* 500特有的样式覆盖 */
.error-number {
  fill: #f39c12; /* 使用橙色表示服务器错误 */
}

.error-icon {
  color: #f39c12;
}

.error-details {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #f39c12;
}

.error-details h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.error-code,
.error-time,
.error-id {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: var(--background-white);
  border-radius: var(--radius-sm);
}

.error-code:last-child,
.error-time:last-child,
.error-id:last-child {
  margin-bottom: 0;
}

.code-label,
.time-label,
.id-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.code-value,
.time-value,
.id-value {
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-weight: 600;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.error-solutions {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.error-solutions h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.error-solutions ul {
  list-style: none;
  padding: 0;
}

.error-solutions li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.error-solutions li::before {
  content: '💡';
  position: absolute;
  left: 0;
}

.contact-info {
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  text-align: left;
}

.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  text-align: center;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-white);
  border-radius: var(--radius-sm);
}

.contact-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.contact-link:hover {
  text-decoration: underline;
}

.contact-link-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

.contact-link-btn:hover {
  color: var(--primary-dark);
}

/* 服务器错误相关的按钮样式 */
.action-btn.primary {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  .nav-btn {
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
  }

  .error-main {
    padding: 1rem;
  }

  .error-content {
    padding: 2rem 1.5rem;
  }

  .error-title {
    font-size: 2rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-label {
    font-size: 0.9rem;
  }

  .error-details,
  .error-solutions,
  .contact-info {
    padding: 1rem;
  }
}
</style>
