package com.example.meals.dto;

/**
 * 管理员注册请求DTO
 */
public class AdminRegisterRequest {
    
    private String username; // 用户名
    private String email;    // 邮箱
    private String realName; // 真实姓名
    private String password; // 密码
    
    // 构造函数
    public AdminRegisterRequest() {}
    
    public AdminRegisterRequest(String username, String email, String realName, String password) {
        this.username = username;
        this.email = email;
        this.realName = realName;
        this.password = password;
    }
    
    // Getter 和 Setter 方法
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    @Override
    public String toString() {
        return "AdminRegisterRequest{" +
                "username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", password='[PROTECTED]'" +
                '}';
    }
}
