{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/passwordstrengthmeter.vue", "../../src/composables/useauth.ts", "../../src/router/guards.ts", "../../src/router/index.ts", "../../src/stores/counter.ts", "../../src/utils/adminapi.ts", "../../src/utils/auth.ts", "../../src/utils/corefeatureapi.ts", "../../src/utils/errorhandler.ts", "../../src/utils/userapi.ts", "../../src/views/admindashboardview.vue", "../../src/views/adminloginview.vue", "../../src/views/adminregisterview.vue", "../../src/views/dashboardview.vue", "../../src/views/error403view.vue", "../../src/views/error404view.vue", "../../src/views/error500view.vue", "../../src/views/errornetworkview.vue", "../../src/views/homeview.vue", "../../src/views/loginview.vue", "../../src/views/registerview.vue"], "version": "5.8.3"}