<template>
  <div class="register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="register-content">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-logo">
          <div class="logo-container">
            <div class="logo-circle">
              <span class="logo-text">🛡️</span>
            </div>
            <div class="logo-ripple"></div>
          </div>
          <h1 class="brand-title">
            <span class="title-char" style="animation-delay: 0.1s">膳</span>
            <span class="title-char" style="animation-delay: 0.2s">食</span>
            <span class="title-char" style="animation-delay: 0.3s">营</span>
            <span class="title-char" style="animation-delay: 0.4s">养</span>
            <span class="title-char" style="animation-delay: 0.5s">分</span>
            <span class="title-char" style="animation-delay: 0.6s">析</span>
            <span class="title-char" style="animation-delay: 0.7s">平</span>
            <span class="title-char" style="animation-delay: 0.8s">台</span>
          </h1>
          <p class="brand-subtitle">管理后台 - 管理员注册</p>
        </div>

        <div class="feature-highlights">
          <div class="feature-item">
            <div class="feature-icon">🔐</div>
            <div class="feature-content">
              <h3>安全管理</h3>
              <p>严格的权限控制体系</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <div class="feature-content">
              <h3>高效运营</h3>
              <p>强大的管理工具集</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📈</div>
            <div class="feature-content">
              <h3>数据洞察</h3>
              <p>全面的分析报告</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">管理员注册</h2>
            <div class="title-underline"></div>
            <p class="form-subtitle">创建新的管理员账户</p>
          </div>

          <!-- 全局错误提示 -->
          <div v-if="globalError" class="global-error-message">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ globalError }}</span>
            </div>
          </div>

          <!-- 成功提示 -->
          <div v-if="successMessage" class="success-message">
            <div class="success-content">
              <svg class="success-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ successMessage }}</span>
            </div>
          </div>

          <form @submit.prevent="handleRegister" class="register-form">
            <div class="form-group">
              <label for="username" class="form-label">管理员用户名</label>
              <div class="input-wrapper">
                <input
                  id="username"
                  v-model="registerForm.username"
                  type="text"
                  class="form-input"
                  :class="{ 'error': errors.username }"
                  placeholder="请输入管理员用户名"
                  @blur="validateUsername"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
            </div>

            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <div class="input-wrapper">
                <input
                  id="email"
                  v-model="registerForm.email"
                  type="email"
                  class="form-input"
                  :class="{ 'error': errors.email }"
                  placeholder="请输入邮箱地址"
                  @blur="validateEmail"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
            </div>

            <div class="form-group">
              <label for="realName" class="form-label">真实姓名</label>
              <div class="input-wrapper">
                <input
                  id="realName"
                  v-model="registerForm.realName"
                  type="text"
                  class="form-input"
                  :class="{ 'error': errors.realName }"
                  placeholder="请输入真实姓名"
                  @blur="validateRealName"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.realName" class="error-message">{{ errors.realName }}</span>
            </div>

            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="input-wrapper">
                <input
                  id="password"
                  v-model="registerForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.password }"
                  placeholder="请输入密码"
                  @blur="validatePassword"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </span>
                <button type="button" class="password-toggle" @click="togglePassword">
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                    <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
            </div>

            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认密码</label>
              <div class="input-wrapper">
                <input
                  id="confirmPassword"
                  v-model="registerForm.confirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.confirmPassword }"
                  placeholder="请再次输入密码"
                  @blur="validateConfirmPassword"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </span>
                <button type="button" class="password-toggle" @click="toggleConfirmPassword">
                  <svg v-if="showConfirmPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                    <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
            </div>

            <button type="submit" class="register-button" :disabled="isLoading">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '注册中...' : '创建管理员账户' }}</span>
            </button>
          </form>

          <div class="form-footer">
            <div class="footer-links">
              <router-link to="/admin/login" class="login-link">已有管理员账户？立即登录</router-link>
              <router-link to="/" class="back-link">← 返回首页</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { adminRegister, checkUsername, checkEmail, type AdminRegisterRequest, type ApiResponse, type Admin } from '../utils/adminApi'

const router = useRouter()

const registerForm = reactive({
  username: '',
  email: '',
  realName: '',
  password: '',
  confirmPassword: ''
})

const errors = reactive({
  username: '',
  email: '',
  realName: '',
  password: '',
  confirmPassword: ''
})

const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const globalError = ref('')
const successMessage = ref('')

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const validateUsername = async () => {
  if (!registerForm.username.trim()) {
    errors.username = '请输入管理员用户名'
    return false
  }
  if (registerForm.username.length < 3) {
    errors.username = '用户名至少需要3个字符'
    return false
  }
  if (registerForm.username.length > 20) {
    errors.username = '用户名不能超过20个字符'
    return false
  }
  if (!/^[a-zA-Z0-9_]+$/.test(registerForm.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    return false
  }

  // 检查用户名是否已存在
  try {
    const response = await checkUsername(registerForm.username)
    if (response.success && !response.data) {
      errors.username = '该用户名已被使用'
      return false
    }
  } catch (error) {
    // 检查用户名失败时静默处理
  }

  errors.username = ''
  return true
}

const validateEmail = async () => {
  if (!registerForm.email.trim()) {
    errors.email = '请输入邮箱地址'
    return false
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(registerForm.email)) {
    errors.email = '请输入有效的邮箱地址'
    return false
  }

  // 检查邮箱是否已存在
  try {
    const response = await checkEmail(registerForm.email)
    if (response.success && !response.data) {
      errors.email = '该邮箱已被使用'
      return false
    }
  } catch (error) {
    // 检查邮箱失败时静默处理
  }

  errors.email = ''
  return true
}

const validateRealName = () => {
  if (!registerForm.realName.trim()) {
    errors.realName = '请输入真实姓名'
    return false
  }
  if (registerForm.realName.length < 2) {
    errors.realName = '真实姓名至少需要2个字符'
    return false
  }
  if (registerForm.realName.length > 10) {
    errors.realName = '真实姓名不能超过10个字符'
    return false
  }
  errors.realName = ''
  return true
}

const validatePassword = () => {
  if (!registerForm.password.trim()) {
    errors.password = '请输入密码'
    return false
  }
  if (registerForm.password.length < 6) {
    errors.password = '密码至少需要6个字符'
    return false
  }
  if (registerForm.password.length > 20) {
    errors.password = '密码不能超过20个字符'
    return false
  }
  errors.password = ''
  return true
}

const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword.trim()) {
    errors.confirmPassword = '请确认密码'
    return false
  }
  if (registerForm.confirmPassword !== registerForm.password) {
    errors.confirmPassword = '两次输入的密码不一致'
    return false
  }
  errors.confirmPassword = ''
  return true
}

const validateForm = async () => {
  const usernameValid = await validateUsername()
  const emailValid = await validateEmail()
  const realNameValid = validateRealName()
  const passwordValid = validatePassword()
  const confirmPasswordValid = validateConfirmPassword()
  
  globalError.value = ''
  successMessage.value = ''

  return usernameValid && emailValid && realNameValid && passwordValid && confirmPasswordValid
}

const handleRegister = async () => {
  if (!(await validateForm())) return

  isLoading.value = true

  try {
    const response: ApiResponse<Admin> = await adminRegister({
      username: registerForm.username,
      email: registerForm.email,
      realName: registerForm.realName,
      password: registerForm.password
    })

    if (response.success) {
      successMessage.value = '管理员账户创建成功！即将跳转到登录页面...'
      
      // 清空表单
      Object.keys(registerForm).forEach(key => {
        registerForm[key as keyof typeof registerForm] = ''
      })
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push('/admin/login')
      }, 3000)
    } else {
      globalError.value = response.message
    }
  } catch (error: any) {
    globalError.value = error.message || '注册失败，请检查网络连接后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* CSS变量定义 - 与登录页面保持一致 */
:root {
  /* 主色调 - 健康绿色系 */
  --primary-green: #16a085;
  --primary-green-light: #48c9b0;
  --primary-green-dark: #138d75;
  --primary-green-ultra-light: #a3e4d7;

  /* 辅助色 - 和谐的蓝绿色系 */
  --secondary-teal: #17a2b8;
  --secondary-teal-light: #5dade2;
  --secondary-teal-dark: #148a99;

  /* 点缀色 - 温暖的橙色系（绿色的互补色） */
  --accent-orange: #e67e22;
  --accent-orange-light: #f39c12;
  --accent-orange-soft: #fdeaa7;

  /* 中性色系 - 进一步优化对比度 */
  --text-primary: #1a252f;
  --text-secondary: #2c3e50;
  --text-light: #34495e;
  --text-muted: #5d6d7e;
  --text-placeholder: #6c757d;

  /* 背景色系 */
  --background-white: #ffffff;
  --background-gray: #f8f9fa;
  --background-light: #ecf0f1;

  /* 边框色系 - 增强可见性 */
  --border-light: #bdc3c7;
  --border-medium: #95a5a6;
  --border-focus: var(--primary-green);

  /* 状态色系 */
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --error-color: #ff4444;
  --error-color-dark: #e74c3c;
  --info-color: #3498db;

  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(44, 62, 80, 0.08);
  --shadow-md: 0 4px 12px 0 rgba(44, 62, 80, 0.12);
  --shadow-lg: 0 8px 25px 0 rgba(44, 62, 80, 0.15);
  --shadow-xl: 0 12px 35px 0 rgba(44, 62, 80, 0.18);

  /* 圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* 主容器 */
.register-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 30%, #e8f6f3 70%, #d5f4e6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.08), rgba(72, 201, 176, 0.04));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.register-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 700px;
}

/* 品牌区域 */
.brand-section {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-teal) 50%, var(--primary-green-light) 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.2;
}

.brand-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.3) 100%);
  pointer-events: none;
}

.brand-logo {
  position: relative;
  z-index: 1;
  margin-bottom: 2rem;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-circle {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-ripple {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: ripple 2s infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.title-char {
  display: inline-block;
  animation: titleSlideIn 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes titleSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.brand-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  font-weight: 400;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.feature-highlights {
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  animation: featureSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateX(-30px);
}

.feature-item:nth-child(1) { animation-delay: 0.2s; }
.feature-item:nth-child(2) { animation-delay: 0.4s; }
.feature-item:nth-child(3) { animation-delay: 0.6s; }

@keyframes featureSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.feature-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.feature-content p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 表单区域 */
.form-section {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--background-white);
  overflow-y: auto;
}

.form-container {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.title-underline {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-green), var(--secondary-teal));
  margin: 0 auto 1rem auto;
  border-radius: 2px;
}

.form-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* 错误和成功提示 */
.global-error-message {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 1.5rem;
  animation: errorSlideIn 0.3s ease-out;
}

.success-message {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border: 1px solid var(--success-color);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 1.5rem;
  animation: successSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-content,
.success-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.error-content {
  color: var(--error-color-dark);
}

.success-content {
  color: var(--success-color);
}

.error-icon,
.success-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* 表单样式 */
.register-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 1rem;
  color: var(--text-primary);
  background: var(--background-white);
  transition: all 0.3s ease;
  outline: none;
}

.form-input::placeholder {
  color: var(--text-placeholder);
}

.form-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
  transform: translateY(-1px);
}

.form-input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  transition: color 0.3s ease;
  z-index: 1;
}

.input-icon svg {
  width: 18px;
  height: 18px;
}

.form-input:focus + .input-icon {
  color: var(--primary-green);
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary-green);
  background: rgba(22, 160, 133, 0.1);
}

.password-toggle svg {
  width: 18px;
  height: 18px;
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  margin-left: 0.25rem;
  animation: errorSlideIn 0.3s ease-out;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #138d75, #16a085, #17a2b8);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(19, 141, 117, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.register-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.register-button:hover::before {
  left: 100%;
}

.register-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #117a65, #138d75, #148a99);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(19, 141, 117, 0.6);
  color: #ffffff;
}

.register-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(19, 141, 117, 0.5);
}

.register-button:disabled {
  background: #95a5a6;
  color: #ffffff;
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  text-shadow: none;
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 表单底部 */
.form-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-light);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.login-link {
  color: var(--secondary-teal);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.login-link:hover {
  color: var(--secondary-teal-dark);
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.15), rgba(23, 162, 184, 0.08));
  border-color: rgba(23, 162, 184, 0.3);
  transform: translateY(-1px);
}

.back-link {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.back-link:hover {
  color: var(--primary-green-dark);
  transform: translateX(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-content {
    grid-template-columns: 1fr;
    max-width: 500px;
    min-height: auto;
  }

  .brand-section {
    padding: 2rem;
    text-align: center;
  }

  .brand-title {
    font-size: 2rem;
  }

  .feature-highlights {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-section {
    padding: 2rem;
  }

  .form-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 0.5rem;
  }

  .brand-section,
  .form-section {
    padding: 1.5rem;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .feature-item {
    padding: 0.75rem;
  }
}
</style>
