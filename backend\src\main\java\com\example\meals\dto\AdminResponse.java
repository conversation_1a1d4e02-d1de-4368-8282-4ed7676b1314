package com.example.meals.dto;

import com.example.meals.entity.Admin;
import java.time.LocalDateTime;

/**
 * 管理员响应DTO（不包含敏感信息）
 */
public class AdminResponse {
    
    private Long id;
    private String username;
    private String email;
    private String realName;
    private String roleName;
    private Integer status;
    private String statusName;
    private LocalDateTime createTime;
    private LocalDateTime lastLoginTime;
    private String token; // JWT Token
    
    // 构造函数
    public AdminResponse() {}
    
    public AdminResponse(Admin admin) {
        this.id = admin.getId();
        this.username = admin.getUsername();
        this.email = admin.getEmail();
        this.realName = admin.getRealName();
        this.roleName = "管理员"; // 统一角色名称
        this.status = admin.getStatus();
        this.statusName = getStatusNameByCode(admin.getStatus());
        this.createTime = admin.getCreateTime();
        this.lastLoginTime = admin.getLastLoginTime();
    }
    

    
    // 根据状态代码获取状态名称
    private String getStatusNameByCode(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "禁用";
            case 1: return "启用";
            default: return "未知";
        }
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    

    
    public String getRoleName() {
        return roleName;
    }
    
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusName() {
        return statusName;
    }
    
    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "AdminResponse{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", roleName='" + roleName + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", createTime=" + createTime +
                ", lastLoginTime=" + lastLoginTime +
                '}';
    }
}
