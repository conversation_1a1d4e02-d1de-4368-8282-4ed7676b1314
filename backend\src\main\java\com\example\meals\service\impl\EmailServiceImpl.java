package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.internet.MimeMessage;

/**
 * 邮件发送服务实现类
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Override
    public Result<Void> sendVerificationCode(String toEmail, String verificationCode, String type) {
        try {
            String subject = getSubjectByType(type);
            String content = buildVerificationEmailContent(verificationCode, type);
            
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail, "膳食营养分析平台");
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(content, true); // 支持HTML格式
            
            mailSender.send(message);
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("验证码邮件发送失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> sendEmail(String toEmail, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("邮件发送失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> testConnection() {
        try {
            // 发送测试邮件到发送方邮箱
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(fromEmail);
            message.setSubject("邮件服务测试");
            message.setText("这是一封测试邮件，用于验证邮件服务配置是否正确。");
            
            mailSender.send(message);
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("邮件连接测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据验证类型获取邮件主题
     */
    private String getSubjectByType(String type) {
        switch (type) {
            case "REGISTER":
                return "【膳食营养分析平台】注册验证码";
            case "RESET_PASSWORD":
                return "【膳食营养分析平台】密码重置验证码";
            default:
                return "【膳食营养分析平台】验证码";
        }
    }
    
    /**
     * 构建验证码邮件内容
     */
    private String buildVerificationEmailContent(String verificationCode, String type) {
        String purpose = getPurposeByType(type);
        
        return String.format(
            "<html>" +
            "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
            "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
            "<div style='text-align: center; margin-bottom: 30px;'>" +
            "<h1 style='color: #4CAF50; margin: 0;'>膳食营养分析平台</h1>" +
            "<p style='color: #666; margin: 5px 0;'>智能营养管理系统</p>" +
            "</div>" +
            "<div style='background: #f9f9f9; padding: 30px; border-radius: 8px; text-align: center;'>" +
            "<h2 style='color: #333; margin-bottom: 20px;'>%s验证码</h2>" +
            "<div style='background: #fff; padding: 20px; border-radius: 4px; margin: 20px 0;'>" +
            "<span style='font-size: 32px; font-weight: bold; color: #4CAF50; letter-spacing: 5px;'>%s</span>" +
            "</div>" +
            "<p style='color: #666; margin: 20px 0;'>验证码有效期为 <strong>5分钟</strong>，请及时使用。</p>" +
            "<p style='color: #999; font-size: 14px;'>如果您没有进行此操作，请忽略此邮件。</p>" +
            "</div>" +
            "<div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>" +
            "<p style='color: #999; font-size: 12px;'>此邮件由系统自动发送，请勿回复。</p>" +
            "</div>" +
            "</div>" +
            "</body>" +
            "</html>",
            purpose, verificationCode
        );
    }
    
    /**
     * 根据验证类型获取用途描述
     */
    private String getPurposeByType(String type) {
        switch (type) {
            case "REGISTER":
                return "注册";
            case "RESET_PASSWORD":
                return "密码重置";
            default:
                return "验证";
        }
    }
}
