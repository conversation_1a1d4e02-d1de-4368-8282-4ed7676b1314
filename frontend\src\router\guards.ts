// 路由守卫

import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { AuthManager, UserManager } from '../utils/auth'

/**
 * 认证守卫 - 检查用户是否已登录
 */
export function authGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  if (AuthManager.isAuthenticated()) {
    next()
  } else {
    // 未登录，重定向到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  }
}

/**
 * 管理员守卫 - 检查用户是否为管理员
 */
export function adminGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  if (!AuthManager.isAuthenticated()) {
    // 未登录，重定向到管理员登录页
    next({
      path: '/admin/login',
      query: { redirect: to.fullPath }
    })
  } else if (UserManager.isAdmin()) {
    next()
  } else {
    // 不是管理员，重定向到首页
    next('/')
  }
}

/**
 * 游客守卫 - 已登录用户不能访问的页面（如登录页）
 */
export function guestGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  if (AuthManager.isAuthenticated()) {
    // 已登录，重定向到首页或指定页面
    const userType = UserManager.getUserType()
    if (userType === 'ADMIN') {
      next('/admin/dashboard')
    } else {
      next('/')
    }
  } else {
    next()
  }
}

/**
 * 用户类型守卫 - 检查用户类型
 */
export function userTypeGuard(allowedTypes: string[]) {
  return (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    if (!AuthManager.isAuthenticated()) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      const userType = UserManager.getUserType()
      if (userType && allowedTypes.includes(userType)) {
        next()
      } else {
        // 用户类型不匹配，根据用户类型重定向到对应页面
        if (userType === 'ADMIN') {
          next('/admin/dashboard')
        } else {
          next('/')
        }
      }
    }
  }
}

/**
 * 管理员重定向守卫 - 已登录的管理员自动重定向到管理后台
 */
export function adminRedirectGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  if (AuthManager.isAuthenticated() && UserManager.isAdmin()) {
    // 已登录的管理员重定向到管理后台
    next('/admin/dashboard')
  } else {
    next()
  }
}
