package com.example.meals.dto;

/**
 * 邮箱验证码登录请求DTO
 */
public class EmailCodeLoginRequest {
    
    private String email;            // 邮箱地址
    private String verificationCode; // 验证码
    
    // 构造函数
    public EmailCodeLoginRequest() {}
    
    public EmailCodeLoginRequest(String email, String verificationCode) {
        this.email = email;
        this.verificationCode = verificationCode;
    }
    
    // Getter 和 Setter 方法
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    @Override
    public String toString() {
        return "EmailCodeLoginRequest{" +
                "email='" + email + '\'' +
                ", verificationCode='[PROTECTED]'" +
                '}';
    }
}
